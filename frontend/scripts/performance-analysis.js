#!/usr/bin/env node

/**
 * Performance Analysis Script for HLenergy Frontend
 * Analyzes bundle size, dependencies, and provides optimization recommendations
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function analyzeBundle() {
  log('\n📦 Bundle Analysis', 'bright')
  log('================', 'bright')

  const distPath = path.join(__dirname, '../dist')
  
  if (!fs.existsSync(distPath)) {
    log('❌ Build not found. Run "npm run build" first.', 'red')
    return
  }

  // Analyze bundle size
  try {
    const bundleSize = execSync(`du -sh ${distPath}`, { encoding: 'utf8' }).trim()
    log(`📊 Total bundle size: ${bundleSize}`, 'cyan')

    // Analyze individual chunks
    const jsDir = path.join(distPath, 'js')
    const cssDir = path.join(distPath, 'css')

    if (fs.existsSync(jsDir)) {
      const jsFiles = fs.readdirSync(jsDir)
        .filter(file => file.endsWith('.js'))
        .map(file => {
          const filePath = path.join(jsDir, file)
          const stats = fs.statSync(filePath)
          return {
            name: file,
            size: stats.size,
            sizeKB: Math.round(stats.size / 1024)
          }
        })
        .sort((a, b) => b.size - a.size)

      log('\n📄 JavaScript Chunks:', 'yellow')
      jsFiles.forEach(file => {
        const color = file.sizeKB > 500 ? 'red' : file.sizeKB > 200 ? 'yellow' : 'green'
        log(`  ${file.name}: ${file.sizeKB}KB`, color)
      })

      // Identify large chunks
      const largeChunks = jsFiles.filter(file => file.sizeKB > 500)
      if (largeChunks.length > 0) {
        log('\n⚠️  Large chunks detected (>500KB):', 'yellow')
        largeChunks.forEach(chunk => {
          log(`  - ${chunk.name} (${chunk.sizeKB}KB)`, 'red')
        })
      }
    }

    if (fs.existsSync(cssDir)) {
      const cssFiles = fs.readdirSync(cssDir)
        .filter(file => file.endsWith('.css'))
        .map(file => {
          const filePath = path.join(cssDir, file)
          const stats = fs.statSync(filePath)
          return {
            name: file,
            size: stats.size,
            sizeKB: Math.round(stats.size / 1024)
          }
        })

      log('\n🎨 CSS Files:', 'yellow')
      cssFiles.forEach(file => {
        const color = file.sizeKB > 100 ? 'red' : file.sizeKB > 50 ? 'yellow' : 'green'
        log(`  ${file.name}: ${file.sizeKB}KB`, color)
      })
    }

  } catch (error) {
    log(`❌ Error analyzing bundle: ${error.message}`, 'red')
  }
}

function analyzeDependencies() {
  log('\n📚 Dependency Analysis', 'bright')
  log('=====================', 'bright')

  const packageJsonPath = path.join(__dirname, '../package.json')
  
  if (!fs.existsSync(packageJsonPath)) {
    log('❌ package.json not found', 'red')
    return
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  const dependencies = packageJson.dependencies || {}
  const devDependencies = packageJson.devDependencies || {}

  log(`📦 Production dependencies: ${Object.keys(dependencies).length}`, 'cyan')
  log(`🔧 Development dependencies: ${Object.keys(devDependencies).length}`, 'cyan')

  // Check for heavy dependencies
  const heavyDeps = [
    'lodash', 'moment', 'jquery', 'bootstrap', 'material-ui',
    'antd', 'react', 'angular', 'vue-cli'
  ]

  const foundHeavyDeps = Object.keys(dependencies).filter(dep => 
    heavyDeps.some(heavy => dep.includes(heavy))
  )

  if (foundHeavyDeps.length > 0) {
    log('\n⚠️  Heavy dependencies detected:', 'yellow')
    foundHeavyDeps.forEach(dep => {
      log(`  - ${dep}`, 'yellow')
    })
  }

  // Check for duplicate functionality
  const duplicateChecks = [
    { deps: ['axios', 'fetch'], type: 'HTTP clients' },
    { deps: ['lodash', 'ramda', 'underscore'], type: 'Utility libraries' },
    { deps: ['moment', 'dayjs', 'date-fns'], type: 'Date libraries' },
    { deps: ['vue-router', 'reach-router'], type: 'Routing libraries' }
  ]

  duplicateChecks.forEach(check => {
    const found = check.deps.filter(dep => dependencies[dep] || devDependencies[dep])
    if (found.length > 1) {
      log(`⚠️  Multiple ${check.type}: ${found.join(', ')}`, 'yellow')
    }
  })
}

function analyzePerformanceMetrics() {
  log('\n⚡ Performance Metrics', 'bright')
  log('====================', 'bright')

  // Check if lighthouse report exists
  const lighthouseReport = path.join(__dirname, '../lighthouse-report.json')
  
  if (fs.existsSync(lighthouseReport)) {
    try {
      const report = JSON.parse(fs.readFileSync(lighthouseReport, 'utf8'))
      const scores = report.lhr.categories

      log('🏆 Lighthouse Scores:', 'cyan')
      Object.entries(scores).forEach(([key, category]) => {
        const score = Math.round(category.score * 100)
        const color = score >= 90 ? 'green' : score >= 70 ? 'yellow' : 'red'
        log(`  ${category.title}: ${score}%`, color)
      })

      // Core Web Vitals
      const audits = report.lhr.audits
      const webVitals = [
        { key: 'first-contentful-paint', name: 'First Contentful Paint' },
        { key: 'largest-contentful-paint', name: 'Largest Contentful Paint' },
        { key: 'cumulative-layout-shift', name: 'Cumulative Layout Shift' },
        { key: 'first-input-delay', name: 'First Input Delay' }
      ]

      log('\n📊 Core Web Vitals:', 'cyan')
      webVitals.forEach(vital => {
        const audit = audits[vital.key]
        if (audit) {
          const value = audit.displayValue || audit.numericValue
          const score = audit.score
          const color = score >= 0.9 ? 'green' : score >= 0.5 ? 'yellow' : 'red'
          log(`  ${vital.name}: ${value}`, color)
        }
      })

    } catch (error) {
      log(`❌ Error reading lighthouse report: ${error.message}`, 'red')
    }
  } else {
    log('📊 No lighthouse report found. Run "npm run lighthouse" to generate one.', 'yellow')
  }
}

function generateRecommendations() {
  log('\n💡 Optimization Recommendations', 'bright')
  log('===============================', 'bright')

  const recommendations = []

  // Check bundle size
  const distPath = path.join(__dirname, '../dist')
  if (fs.existsSync(distPath)) {
    try {
      const bundleSize = execSync(`du -s ${distPath}`, { encoding: 'utf8' })
      const sizeKB = parseInt(bundleSize.split('\t')[0])
      
      if (sizeKB > 2000) { // 2MB
        recommendations.push('🔧 Bundle size is large (>2MB). Consider code splitting and lazy loading.')
      }
      
      if (sizeKB > 5000) { // 5MB
        recommendations.push('🚨 Bundle size is very large (>5MB). Implement aggressive code splitting.')
      }
    } catch (error) {
      // Ignore error
    }
  }

  // Check for optimization opportunities
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'))
  const dependencies = packageJson.dependencies || {}

  if (dependencies['lodash']) {
    recommendations.push('📦 Consider replacing lodash with native ES6+ methods or lodash-es for tree shaking.')
  }

  if (dependencies['moment']) {
    recommendations.push('📅 Consider replacing moment.js with day.js or date-fns for smaller bundle size.')
  }

  if (!dependencies['vue'] || !dependencies['vue'].includes('3.')) {
    recommendations.push('🔄 Consider upgrading to Vue 3 for better performance and smaller bundle size.')
  }

  // Check Vite config
  const viteConfigPath = path.join(__dirname, '../vite.config.ts')
  if (fs.existsSync(viteConfigPath)) {
    const viteConfig = fs.readFileSync(viteConfigPath, 'utf8')
    
    if (!viteConfig.includes('rollupOptions')) {
      recommendations.push('⚙️  Add Rollup optimization options to Vite config for better chunking.')
    }
    
    if (!viteConfig.includes('terser')) {
      recommendations.push('🗜️  Enable Terser minification for smaller production bundles.')
    }
  }

  // General recommendations
  recommendations.push('🖼️  Optimize images: Use WebP format and responsive images.')
  recommendations.push('🔄 Implement service worker caching for better performance.')
  recommendations.push('📱 Use critical CSS inlining for faster first paint.')
  recommendations.push('🚀 Consider using a CDN for static assets.')

  if (recommendations.length > 0) {
    recommendations.forEach(rec => log(`  ${rec}`, 'cyan'))
  } else {
    log('✅ No major optimization opportunities found!', 'green')
  }
}

function checkPerformanceConfig() {
  log('\n⚙️  Performance Configuration', 'bright')
  log('============================', 'bright')

  const checks = [
    {
      file: 'vite.config.ts',
      patterns: [
        { pattern: 'rollupOptions', name: 'Bundle optimization' },
        { pattern: 'terser', name: 'Minification' },
        { pattern: 'compression', name: 'Gzip compression' },
        { pattern: 'manualChunks', name: 'Code splitting' }
      ]
    },
    {
      file: 'package.json',
      patterns: [
        { pattern: '"type": "module"', name: 'ES modules' },
        { pattern: 'lighthouse', name: 'Performance testing' }
      ]
    }
  ]

  checks.forEach(check => {
    const filePath = path.join(__dirname, `../${check.file}`)
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      log(`\n📄 ${check.file}:`, 'yellow')
      check.patterns.forEach(pattern => {
        const found = content.includes(pattern.pattern)
        const status = found ? '✅' : '❌'
        const color = found ? 'green' : 'red'
        log(`  ${status} ${pattern.name}`, color)
      })
    }
  })
}

// Main execution
function main() {
  log('🚀 HLenergy Frontend Performance Analysis', 'bright')
  log('=========================================', 'bright')

  analyzeBundle()
  analyzeDependencies()
  analyzePerformanceMetrics()
  checkPerformanceConfig()
  generateRecommendations()

  log('\n✨ Analysis complete!', 'green')
  log('💡 Use "npm run lighthouse" to generate detailed performance metrics.', 'cyan')
  log('🔧 Use "npm run analyze:bundle" to visualize bundle composition.', 'cyan')
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export {
  analyzeBundle,
  analyzeDependencies,
  analyzePerformanceMetrics,
  generateRecommendations,
  checkPerformanceConfig
}
