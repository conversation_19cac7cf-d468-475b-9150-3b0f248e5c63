#!/bin/bash

# HLenergy Frontend Dependencies Update Script
# This script safely updates dependencies with backup and testing

echo "🔄 Starting HLenergy Frontend Dependencies Update..."

# Create backup of current package files
echo "📦 Creating backup of package files..."
cp package.json package.json.backup
cp package-lock.json package-lock.json.backup

# Check current outdated packages
echo "📊 Checking outdated packages..."
npm outdated

# Ask for confirmation
read -p "Do you want to proceed with updates? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Update cancelled"
    exit 1
fi

# Update patch and minor versions (safe)
echo "🔧 Updating patch and minor versions..."
npm update

# Check if user wants to update major versions
read -p "Do you want to update major versions? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "⚠️  Updating major versions (may include breaking changes)..."
    
    # Core dependencies
    npm install vue@latest @vue/router@latest pinia@latest
    
    # Build tools
    npm install vite@latest @vitejs/plugin-vue@latest
    
    # UI framework
    npm install daisyui@latest tailwindcss@latest
    
    # Firebase
    npm install firebase@latest
    
    # Socket.io
    npm install socket.io-client@latest
    
    # Development dependencies
    npm install -D typescript@latest @types/node@latest eslint@latest
fi

# Clean install
echo "🧹 Performing clean install..."
rm -rf node_modules
npm install

# Test build
echo "🏗️  Testing build..."
if npm run build; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed! Restoring backup..."
    cp package.json.backup package.json
    cp package-lock.json.backup package-lock.json
    npm install
    echo "🔄 Backup restored. Please check for breaking changes."
    exit 1
fi

# Test development server
echo "🚀 Testing development server..."
timeout 10s npm run dev || echo "⚠️  Dev server test completed"

# Show updated packages
echo "📋 Updated packages:"
npm list --depth=0 --json | jq -r '.dependencies | keys[]' | head -20

# Cleanup backup files
read -p "Update successful! Remove backup files? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm package.json.backup package-lock.json.backup
    echo "🗑️  Backup files removed"
else
    echo "💾 Backup files kept: package.json.backup, package-lock.json.backup"
fi

echo "✅ Dependencies update completed successfully!"
echo "🔍 Remember to test your application thoroughly"
echo "📚 Check changelogs for any breaking changes"
