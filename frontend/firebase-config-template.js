// Firebase Configuration Template
// Copy this to src/plugins/firebase.js and replace with your actual values

const firebaseConfig = {
  // Get these values from Firebase Console > Project Settings > General > Your apps
  apiKey: "AIzaSyC...", // Your API key
  authDomain: "your-project.firebaseapp.com", // Your project domain
  projectId: "your-project-id", // Your project ID
  storageBucket: "your-project.appspot.com", // Your storage bucket
  messagingSenderId: "123456789", // Your sender ID
  appId: "1:123456789:web:abc123def456", // Your app ID
  measurementId: "G-XXXXXXXXXX" // Your measurement ID (for Analytics)
}

// Steps to get your config:
// 1. Go to https://console.firebase.google.com/
// 2. Select your project (or create new one)
// 3. Click the gear icon (Project Settings)
// 4. Scroll down to "Your apps" section
// 5. Click "Add app" and select Web (</>) if you haven't already
// 6. Register your app with nickname "HLenergy Web App"
// 7. Copy the config object shown
// 8. Replace the values in src/plugins/firebase.js

// For Analytics:
// 1. In Firebase Console, go to Analytics
// 2. Click "Enable Google Analytics"
// 3. Link to existing Google Analytics account or create new one
// 4. Your measurementId will be generated automatically

export default firebaseConfig
