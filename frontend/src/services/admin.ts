import { apiService } from './api'

// Admin Dashboard Types
export interface DashboardStats {
  overview: {
    totalUsers: number
    activeUsers: number
    totalContacts: number
    newContacts: number
    totalLogs: number
    recentErrors: number
  }
  usersByRole: Record<string, number>
  contactsByStatus: Record<string, number>
}

export interface SystemHealth {
  database: {
    status: string
    responseTime?: number
    connections?: number
  }
  logging: {
    status: string
    totalLogs?: number
    errorRate?: number
  }
  system: {
    uptime: number
    uptimeFormatted: string
    memory: {
      heapUsed: number
      heapTotal: number
      external: number
    }
    memoryFormatted: {
      used: string
      total: string
      external: string
    }
    cpu: {
      user: number
      system: number
    }
    platform: string
    nodeVersion: string
    pid: number
  }
  health: {
    recentErrors: number
    activeSessions: number
    status: 'healthy' | 'warning' | 'error'
  }
}

export interface SocketMetrics {
  activeConnections: number
  totalConnections: number
  peakConnections: number
  uptime: number
  events: {
    totalEvents: number
    eventsPerSecond: number
    averageResponseTime: number
  }
  rooms: {
    totalRooms: number
    adminRooms: number
    userRooms: number
  }
  performance: {
    memoryUsage: number
    cpuUsage: number
    averageResponseTime: number
  }
}

export interface MemoryStats {
  memory: {
    heapUsed: number
    heapTotal: number
    maps: {
      connectedUsers: number
      connectionAttempts: number
      eventsByType: number
    }
  }
  process: {
    heapUsed: number
    heapTotal: number
    rss: number
    external: number
    arrayBuffers: number
  }
  efficiency: {
    connectionsPerMB: number
    eventsPerMB: number
    memoryGrowthRate: number
  }
  health: {
    status: 'healthy' | 'warning' | 'error'
    warnings: string[]
    uptime: number
    lastCleanup: string
  }
}

export interface AnalyticsData {
  timeframe: string
  analytics: {
    userRegistrations: Array<{ date: string; count: number }>
    contactSubmissions: Record<string, Record<string, number>>
    authEvents: Array<{ date: string; count: number }>
    performance: Array<{
      date: string
      avgResponseTime: number
      maxResponseTime: number
      requestCount: number
    }>
  }
}

export interface User {
  id: number
  name: string
  email: string
  role: 'admin' | 'staff' | 'client'
  is_active: boolean
  email_verified: boolean
  created_at: string
  updated_at: string
  last_login?: string
}

export interface ContactSubmission {
  id: number
  name: string
  email: string
  phone?: string
  company?: string
  message: string
  status: 'new' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assigned_to?: number
  notes?: string
  created_at: string
  updated_at: string
  resolved_at?: string
  closed_at?: string
}

// Admin Service Class
class AdminService {
  // Dashboard Statistics
  async getDashboardStats(): Promise<DashboardStats> {
    return await apiService.get<DashboardStats>('/admin/dashboard')
  }

  // User Dashboard Stats (simplified for regular users)
  async getUserDashboardStats(): Promise<{
    users: {
      total: number
      active: number
    }
    contacts: {
      total: number
      today: number
    }
    leads: {
      total: number
      today: number
      qualified: number
    }
    system: {
      uptime: number
      status: string
      responseTime: number
    }
    performance: {
      conversionRate: number
      successRate: number
    }
  }> {
    return await apiService.get('/admin/user-dashboard/stats')
  }

  // Comprehensive Dashboard Stats Cards
  async getDashboardStatsCards(): Promise<{
    users: {
      total: number
      active: number
      newToday: number
      newThisWeek: number
      trend: number
      growthRate: number
    }
    contacts: {
      total: number
      new: number
      today: number
      thisWeek: number
      trend: number
      conversionRate: number
    }
    leads: {
      total: number
      hot: number
      newToday: number
      newThisWeek: number
      trend: number
      conversionRate: number
    }
    emails: {
      pending: number
      sent: number
      failed: number
      today: number
      successRate: number
      queueHealth: string
    }
    system: {
      health: number
      status: string
      uptime: number
      uptimeFormatted: string
      activeSessions: number
      responseTime: number
      memoryUsage: number
      memoryUsed: number
      cpuUsage: number
    }
    logs: {
      total: number
      errors: number
      warnings: number
      info: number
      errorRate: number
    }
    performance: {
      avgResponseTime: number
      requestsToday: number
      errorRate: number
      successRate: number
      throughput: number
    }
    realtime: {
      onlineUsers: number
      activeConnections: number
      eventsPerSecond: number
      lastUpdated: string
    }
  }> {
    return await apiService.get('/admin/dashboard/stats')
  }

  // System Health
  async getSystemHealth(): Promise<SystemHealth> {
    return await apiService.get<SystemHealth>('/admin/system/health')
  }

  // Socket.io Metrics
  async getSocketMetrics(): Promise<SocketMetrics> {
    return await apiService.get<SocketMetrics>('/admin/socket/metrics')
  }

  // Memory Statistics
  async getMemoryStats(): Promise<MemoryStats> {
    return await apiService.get<MemoryStats>('/socket/memory')
  }

  // Analytics Data
  async getAnalytics(timeframe: '24h' | '7d' | '30d' | '90d' = '7d'): Promise<AnalyticsData> {
    return await apiService.get<AnalyticsData>(`/admin/analytics?timeframe=${timeframe}`)
  }

  // System Alerts
  async getSystemAlerts(): Promise<{
    alerts: Array<{
      id: number
      level: 'info' | 'warning' | 'error'
      title: string
      message: string
      timestamp: string
      category: string
    }>
  }> {
    return await apiService.get('/admin/alerts')
  }

  // Live Activity
  async getLiveActivity(limit: number = 50): Promise<{
    activities: Array<{
      id: number
      type: string
      title: string
      description: string
      timestamp: string
      severity: string
      user?: {
        id: number
        name: string
        email: string
      }
      metadata?: any
    }>
  }> {
    return await apiService.get(`/admin/activity?limit=${limit}`)
  }

  // Performance Metrics
  async getPerformanceMetrics(period: '1h' | '6h' | '24h' | '7d' = '24h'): Promise<{
    period: string
    current: {
      memoryUsage: number
      memoryUsed: number
      cpuUsage: number
      uptime: number
      timestamp: string
    }
    historical: Array<{
      timestamp: string
      avgResponseTime: number
      maxResponseTime: number
      requestCount: number
    }>
  }> {
    return await apiService.get(`/admin/performance?period=${period}`)
  }

  // Quick Actions
  async executeQuickAction(action: string, data?: any): Promise<{
    action: string
    result: any
    executedBy: {
      id: number
      name: string
      email: string
    }
  }> {
    return await apiService.post(`/admin/actions/${action}`, data)
  }

  // User Management
  async getUsers(params?: {
    page?: number
    limit?: number
    role?: string
    active?: boolean
  }): Promise<{
    users: User[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.role) queryParams.append('role', params.role)
    if (params?.active !== undefined) queryParams.append('active', params.active.toString())

    const query = queryParams.toString()
    return await apiService.get(`/admin/users${query ? `?${query}` : ''}`)
  }

  async getUserDetails(id: number): Promise<{
    user: User
    recentActivity: any[]
  }> {
    return await apiService.get(`/admin/users/${id}`)
  }

  async updateUserStatus(id: number, isActive: boolean, reason?: string): Promise<{
    user: {
      id: number
      email: string
      is_active: boolean
    }
  }> {
    return await apiService.patch(`/admin/users/${id}/status`, {
      is_active: isActive,
      reason
    })
  }

  // Contact Management
  async getContacts(params?: {
    page?: number
    limit?: number
    status?: string
    priority?: string
  }): Promise<{
    contacts: ContactSubmission[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.status) queryParams.append('status', params.status)
    if (params?.priority) queryParams.append('priority', params.priority)

    const query = queryParams.toString()
    return await apiService.get(`/admin/contacts${query ? `?${query}` : ''}`)
  }

  async updateContact(id: number, updates: {
    status?: string
    priority?: string
    assigned_to?: number
    notes?: string
  }): Promise<{
    contact: ContactSubmission
  }> {
    return await apiService.patch(`/admin/contacts/${id}`, updates)
  }

  // System Operations
  async forceMemoryCleanup(): Promise<{
    before: any
    after: any
    improvement: {
      heapReduced: number
      mapsReduced: number
    }
  }> {
    return await apiService.post('/socket/cleanup')
  }

  // Authentication Methods
  async getAuthMethods(): Promise<{
    methods: string[]
    hasPin: boolean
    biometricCount: number
    setupComplete: boolean
  }> {
    return await apiService.get('/admin/auth-methods')
  }

  // Email Worker Management
  async getEmailWorkerStatus(): Promise<{
    data: {
      status: {
        isRunning: boolean
        lastProcessed: string | null
        processId: number | null
        uptime: string | null
        method: string
        status: string
        started_at: string | null
        stopped_at: string | null
        last_heartbeat: string | null
        uptime_seconds: number
        processed_count: number
        error_count: number
        details: any
      }
      statistics: {
        pending: number
        processing: number
        sent: number
        failed: number
        total: number
      }
      health: {
        queueBacklog: boolean
        oldFailures: boolean
        recentFailures: number
        processingStuck: boolean
      }
      recentEmails: any[]
      worker: {
        location: string
        commands: any
      }
    }
  }> {
    return await apiService.getRaw('/admin/email-worker/status')
  }

  async getEmailQueue(params: {
    page?: number
    limit?: number
    status?: string
    search?: string
  } = {}): Promise<{
    items: any[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    return await apiService.get('/admin/email-worker/queue', params)
  }

  async retryEmail(emailId: number): Promise<{
    message: string
    email: any
  }> {
    return await apiService.post(`/admin/email-worker/retry/${emailId}`)
  }

  async clearFailedEmails(): Promise<{
    message: string
    deletedCount: number
  }> {
    return await apiService.post('/admin/email-worker/clear-failed')
  }
}

// Export singleton instance
export const adminService = new AdminService()
export default adminService
