/**
 * Network Status Manager
 * Centralized handling of online/offline states to prevent request spamming
 */

class NetworkStatusManager {
  private isOnline: boolean = navigator.onLine
  private listeners: Set<(isOnline: boolean) => void> = new Set()
  private requestQueue: Map<string, () => Promise<any>> = new Map()
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map()

  constructor() {
    this.setupEventListeners()
  }

  private setupEventListeners() {
    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))
  }

  private handleOnline() {
    console.log('🌐 Network status: ONLINE')
    this.isOnline = true
    this.notifyListeners(true)
    this.processQueuedRequests()
  }

  private handleOffline() {
    console.log('🌐 Network status: OFFLINE')
    this.isOnline = false
    this.notifyListeners(false)
    this.clearRetryTimeouts()
  }

  private notifyListeners(isOnline: boolean) {
    this.listeners.forEach(listener => {
      try {
        listener(isOnline)
      } catch (error) {
        console.error('Error in network status listener:', error)
      }
    })
  }

  private async processQueuedRequests() {
    if (!this.isOnline) return

    console.log(`🔄 Processing ${this.requestQueue.size} queued requests`)
    
    for (const [key, request] of this.requestQueue.entries()) {
      try {
        await request()
        this.requestQueue.delete(key)
        console.log(`✅ Queued request processed: ${key}`)
      } catch (error) {
        console.error(`❌ Failed to process queued request ${key}:`, error)
        // Keep in queue for next online event
      }
    }
  }

  private clearRetryTimeouts() {
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout))
    this.retryTimeouts.clear()
  }

  /**
   * Check if device is currently online
   */
  getIsOnline(): boolean {
    return this.isOnline
  }

  /**
   * Subscribe to network status changes
   */
  subscribe(listener: (isOnline: boolean) => void): () => void {
    this.listeners.add(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * Execute a request only if online, otherwise queue it
   */
  async executeWhenOnline<T>(
    key: string,
    request: () => Promise<T>,
    options: {
      immediate?: boolean // Execute immediately if online
      retryDelay?: number // Delay before retrying when back online
    } = {}
  ): Promise<T | null> {
    const { immediate = true, retryDelay = 0 } = options

    if (this.isOnline && immediate) {
      try {
        return await request()
      } catch (error: any) {
        // If request fails due to network, queue it
        if (this.isNetworkError(error)) {
          console.log(`🌐 Network error detected, queueing request: ${key}`)
          this.queueRequest(key, request, retryDelay)
        }
        throw error
      }
    } else {
      console.log(`🌐 Offline - queueing request: ${key}`)
      this.queueRequest(key, request, retryDelay)
      return null
    }
  }

  /**
   * Queue a request to be executed when online
   */
  private queueRequest(key: string, request: () => Promise<any>, delay: number = 0) {
    // Clear any existing timeout for this key
    const existingTimeout = this.retryTimeouts.get(key)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    if (delay > 0) {
      const timeout = setTimeout(() => {
        this.requestQueue.set(key, request)
        this.retryTimeouts.delete(key)
      }, delay)
      this.retryTimeouts.set(key, timeout)
    } else {
      this.requestQueue.set(key, request)
    }
  }

  /**
   * Check if an error is network-related
   */
  private isNetworkError(error: any): boolean {
    return (
      error.code === 'NETWORK_ERROR' ||
      error.code === 'OFFLINE' ||
      !navigator.onLine ||
      error.message?.includes('Network Error') ||
      error.message?.includes('fetch')
    )
  }

  /**
   * Remove a queued request
   */
  removeQueuedRequest(key: string) {
    this.requestQueue.delete(key)
    const timeout = this.retryTimeouts.get(key)
    if (timeout) {
      clearTimeout(timeout)
      this.retryTimeouts.delete(key)
    }
  }

  /**
   * Get number of queued requests
   */
  getQueuedRequestCount(): number {
    return this.requestQueue.size
  }

  /**
   * Clear all queued requests
   */
  clearQueue() {
    this.requestQueue.clear()
    this.clearRetryTimeouts()
  }

  /**
   * Cleanup - remove event listeners
   */
  destroy() {
    window.removeEventListener('online', this.handleOnline.bind(this))
    window.removeEventListener('offline', this.handleOffline.bind(this))
    this.listeners.clear()
    this.clearQueue()
  }
}

// Create singleton instance
export const networkStatus = new NetworkStatusManager()

// Export types
export type NetworkStatusListener = (isOnline: boolean) => void

// Utility functions
export const isOnline = () => networkStatus.getIsOnline()
export const onNetworkChange = (listener: NetworkStatusListener) => networkStatus.subscribe(listener)
export const executeWhenOnline = <T>(key: string, request: () => Promise<T>, options?: any) => 
  networkStatus.executeWhenOnline(key, request, options)
