// Offline Cache Service for comprehensive page caching
export interface CachedPage {
  url: string
  html: string
  timestamp: number
  version: string
}

export interface CacheStats {
  totalPages: number
  totalSize: number
  lastUpdated: Date
  cacheVersion: string
}

class OfflineCacheService {
  private readonly CACHE_NAME = 'hlenergy-pages-v1'
  private readonly CACHE_VERSION = '1.0.0'
  private readonly MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB
  private readonly MAX_PAGES = 100

  // Essential pages that should always be cached
  // Note: Only caching root page for SPA - other routes handled by service worker fallback
  private readonly ESSENTIAL_PAGES = [
    '/' // Root page serves all SPA routes via Vue Router
  ]

  /**
   * Initialize the offline cache
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing offline cache service...')
      
      // Check if service worker is available
      if (!('serviceWorker' in navigator)) {
        console.warn('Service Worker not supported')
        return
      }

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Cache essential pages
      await this.cacheEssentialPages()
      
      // Set up page visit listener
      this.setupPageVisitListener()
      
      console.log('Offline cache service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize offline cache:', error)
    }
  }

  /**
   * Cache essential pages for offline access
   * Note: Disabled for SPA routes - these are handled by service worker navigation fallback
   */
  private async cacheEssentialPages(): Promise<void> {
    try {
      console.log('Essential pages caching disabled for SPA routes')
      console.log('SPA routes are handled by service worker navigation fallback to index.html')

      // Only cache the root index.html which serves all SPA routes
      const rootPage = '/'

      try {
        // Check if root page is already cached
        const isCached = await this.isPageCached(rootPage)
        if (isCached) {
          console.log(`Essential page ${rootPage} already cached, skipping`)
          return
        }

        console.log(`Caching essential page: ${rootPage}`)
        const cache = await caches.open(this.CACHE_NAME)

        const response = await fetch(rootPage, {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })

        if (response.ok) {
          // Clone response and add cache timestamp
          const responseClone = response.clone()
          const headers = new Headers(responseClone.headers)
          headers.set('sw-cache-date', new Date().toISOString())

          const cachedResponse = new Response(responseClone.body, {
            status: responseClone.status,
            statusText: responseClone.statusText,
            headers: headers
          })

          await cache.put(rootPage, cachedResponse)
          console.log(`✅ Cached essential page: ${rootPage}`)
        }
      } catch (error) {
        console.warn(`Failed to cache essential page ${rootPage}:`, error)
      }
    } catch (error) {
      console.error('Failed to cache essential pages:', error)
    }
  }

  /**
   * Set up listener to cache pages as user visits them
   */
  private setupPageVisitListener(): void {
    // For SPA, we only need to cache the root page
    // Individual routes are handled by Vue Router and don't need separate caching
    console.log('Setting up page visit listener for SPA (root page only)')

    // Only cache root page initially
    if (window.location.pathname === '/') {
      this.throttledCachePage(window.location.pathname)
    }

    // Note: We don't need to listen for SPA route changes since they don't require separate caching
    // The service worker will handle offline fallback to the cached root page
  }

  // Throttle page caching to avoid rapid repeated calls
  private lastCacheAttempt: { [url: string]: number } = {}
  private readonly CACHE_THROTTLE_MS = 5000 // 5 seconds

  private throttledCachePage(url: string): void {
    // Skip SPA routes - only cache root page
    if (url !== '/' && !url.includes('.')) {
      console.log(`Skipping SPA route cache for ${url} - not needed for SPA`)
      return
    }

    const now = Date.now()
    const lastAttempt = this.lastCacheAttempt[url] || 0

    if (now - lastAttempt < this.CACHE_THROTTLE_MS) {
      console.log(`Throttling cache attempt for ${url} (last attempt ${Math.round((now - lastAttempt) / 1000)}s ago)`)
      return
    }

    this.lastCacheAttempt[url] = now
    this.cachePage(url)
  }

  /**
   * Cache a specific page (SPA-aware)
   */
  async cachePage(url: string): Promise<void> {
    try {
      // Skip caching for certain URLs
      if (this.shouldSkipCaching(url)) {
        console.log(`Skipping cache for ${url} - matches skip pattern`)
        return
      }

      // For SPA routes (anything other than root), we don't need to cache them separately
      // since they're all served by the root index.html file
      if (url !== '/' && !url.includes('.')) {
        console.log(`Skipping SPA route cache for ${url} - handled by root page`)
        return
      }

      const cache = await caches.open(this.CACHE_NAME)

      // Check if page is already cached and recent
      const cachedResponse = await cache.match(url)
      if (cachedResponse) {
        const cacheDate = cachedResponse.headers.get('sw-cache-date')
        if (cacheDate) {
          const cacheTime = new Date(cacheDate).getTime()
          const now = Date.now()
          const oneHour = 60 * 60 * 1000

          // Skip if cached less than an hour ago
          if (now - cacheTime < oneHour) {
            console.log(`Page ${url} already cached recently, skipping`)
            return
          } else {
            console.log(`Page ${url} cache is stale, updating...`)
          }
        } else {
          console.log(`Page ${url} cached but no timestamp, updating...`)
        }
      } else {
        console.log(`Caching new page: ${url}`)
      }

      // Fetch and cache the page
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (response.ok) {
        // Clone response and add cache timestamp
        const responseClone = response.clone()
        const headers = new Headers(responseClone.headers)
        headers.set('sw-cache-date', new Date().toISOString())

        const cachedResponse = new Response(responseClone.body, {
          status: responseClone.status,
          statusText: responseClone.statusText,
          headers: headers
        })

        await cache.put(url, cachedResponse)
        console.log(`Page cached: ${url}`)

        // Clean up old cache entries if needed
        await this.cleanupCache()
      }
    } catch (error) {
      console.warn(`Failed to cache page ${url}:`, error)
    }
  }

  /**
   * Check if URL should be skipped for caching
   */
  private shouldSkipCaching(url: string): boolean {
    const skipPatterns = [
      /\/api\//,
      /\.(json|xml|txt)$/,
      /\?/,
      /#/
    ]

    // Skip all SPA routes except root - they're handled by the root index.html
    if (url !== '/' && !url.includes('.')) {
      return true
    }

    return skipPatterns.some(pattern => pattern.test(url))
  }

  /**
   * Clean up old cache entries
   */
  private async cleanupCache(): Promise<void> {
    try {
      const cache = await caches.open(this.CACHE_NAME)
      const requests = await cache.keys()

      if (requests.length <= this.MAX_PAGES) {
        return
      }

      // Sort by cache date and remove oldest entries
      const requestsWithDates = await Promise.all(
        requests.map(async (request) => {
          const response = await cache.match(request)
          const cacheDate = response?.headers.get('sw-cache-date')
          return {
            request,
            date: cacheDate ? new Date(cacheDate).getTime() : 0
          }
        })
      )

      requestsWithDates.sort((a, b) => a.date - b.date)

      // Remove oldest entries
      const toRemove = requestsWithDates.slice(0, requests.length - this.MAX_PAGES)
      for (const { request } of toRemove) {
        await cache.delete(request)
        console.log(`Removed old cache entry: ${request.url}`)
      }
    } catch (error) {
      console.error('Failed to cleanup cache:', error)
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<CacheStats> {
    try {
      const cache = await caches.open(this.CACHE_NAME)
      const requests = await cache.keys()
      
      let totalSize = 0
      let lastUpdated = new Date(0)

      for (const request of requests) {
        const response = await cache.match(request)
        if (response) {
          const blob = await response.blob()
          totalSize += blob.size

          const cacheDate = response.headers.get('sw-cache-date')
          if (cacheDate) {
            const date = new Date(cacheDate)
            if (date > lastUpdated) {
              lastUpdated = date
            }
          }
        }
      }

      return {
        totalPages: requests.length,
        totalSize,
        lastUpdated,
        cacheVersion: this.CACHE_VERSION
      }
    } catch (error) {
      console.error('Failed to get cache stats:', error)
      return {
        totalPages: 0,
        totalSize: 0,
        lastUpdated: new Date(),
        cacheVersion: this.CACHE_VERSION
      }
    }
  }

  /**
   * Clear all cached pages
   */
  async clearCache(): Promise<void> {
    try {
      await caches.delete(this.CACHE_NAME)
      console.log('Cache cleared successfully')
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }

  /**
   * Preload specific pages
   */
  async preloadPages(urls: string[]): Promise<void> {
    console.log('Preloading pages:', urls)

    // Filter out already cached pages
    const uncachedUrls: string[] = []
    for (const url of urls) {
      const isCached = await this.isPageCached(url)
      if (!isCached) {
        uncachedUrls.push(url)
      } else {
        console.log(`Page ${url} already cached, skipping preload`)
      }
    }

    if (uncachedUrls.length === 0) {
      console.log('All requested pages are already cached')
      return
    }

    console.log(`Preloading ${uncachedUrls.length} uncached pages:`, uncachedUrls)

    for (const url of uncachedUrls) {
      await this.cachePage(url)
      // Add small delay to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  /**
   * Check if page is available offline
   */
  async isPageCached(url: string): Promise<boolean> {
    try {
      const cache = await caches.open(this.CACHE_NAME)
      const response = await cache.match(url)
      return !!response
    } catch (error) {
      console.error('Failed to check cache:', error)
      return false
    }
  }

  /**
   * Get list of cached pages
   */
  async getCachedPages(): Promise<string[]> {
    try {
      const cache = await caches.open(this.CACHE_NAME)
      const requests = await cache.keys()
      return requests.map(request => request.url)
    } catch (error) {
      console.error('Failed to get cached pages:', error)
      return []
    }
  }
}

// Export singleton instance
export const offlineCacheService = new OfflineCacheService()
export default offlineCacheService
