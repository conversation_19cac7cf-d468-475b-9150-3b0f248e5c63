import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import { useAuthStore } from '@/stores/auth'
import { sessionService } from '@/services/session'

// Performance optimization: Cache environment check
const isDevelopment = import.meta.env.DEV
const isDebugMode = isDevelopment && import.meta.env.VITE_ENABLE_DEBUG !== 'false'

// Performance optimization: Pre-compile auth endpoint patterns
const AUTH_ENDPOINT_PATTERNS = [
  '/auth/login',
  '/auth/register',
  '/auth/refresh',
  '/auth/forgot-password',
  '/auth/reset-password'
] as const

// Fast auth endpoint check using startsWith (faster than includes)
const isAuthEndpoint = (url: string): boolean => {
  return AUTH_ENDPOINT_PATTERNS.some(pattern => url.includes(pattern))
}

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
const API_VERSION = 'v1'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    type?: string
    code?: string
  }
  message?: string
  timestamp?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// API Error Class
export class ApiError extends Error {
  public status: number
  public code?: string
  public type?: string

  constructor(message: string, status: number, code?: string, type?: string) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
    this.type = type
  }
}

// Create Axios Instance
class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Optimized Request Interceptor - Minimal overhead
    this.api.interceptors.request.use(
      (config) => {
        // Development-only debug logging (removed from production)
        if (isDebugMode && config.url?.includes('/auth/login')) {
          console.log('🔍 [API DEBUG] Login request:', {
            url: config.url,
            method: config.method,
            // Reduced logging payload for performance
            hasData: !!config.data
          })
        }

        // Fast auth token injection
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => {
        // Development-only error logging
        if (isDebugMode) {
          console.error('🔍 [API DEBUG] Request error:', error.message || error)
        }
        return Promise.reject(error)
      }
    )

    // Optimized Response Interceptor - Minimal success path overhead
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // Development-only debug logging with reduced payload
        if (isDebugMode && response.config.url?.includes('/auth/login')) {
          console.log('🔍 [API DEBUG] Login response:', {
            status: response.status,
            success: !!response.data?.success
          })
        }
        return response
      },
      (error: AxiosError<ApiResponse>) => {
        // Development-only debug logging with minimal payload
        if (isDebugMode && error.config?.url?.includes('/auth/login')) {
          console.error('🔍 [API DEBUG] Login error:', {
            status: error.response?.status,
            message: error.message
          })
        }

        // Fast status code checks
        const status = error.response?.status
        const requestUrl = error.config?.url || ''

        // Handle 423 - Session Locked (optimized)
        if (status === 423) {
          if (isDebugMode) {
            console.log('🔒 API call blocked - session is locked')
          }
          sessionService.handleSessionLockResponse(error)
          throw new ApiError('Session is locked', 423)
        }

        // Handle 401 - Unauthorized (optimized)
        if (status === 401) {
          // Use optimized auth endpoint check
          if (!isAuthEndpoint(requestUrl)) {
            // This is a session expiry on a protected endpoint
            if (isDebugMode) {
              console.log('🔒 Session expired - logging out user')
            }
            const authStore = useAuthStore()
            authStore.logout()
            window.location.href = '/login'
            return Promise.reject(new ApiError('Session expired', 401))
          }

          // For auth endpoints, just pass through the error without logout
          if (isDebugMode) {
            console.log('❌ Authentication failed on auth endpoint:', requestUrl)
          }
        }

        // Handle API errors (optimized)
        if (error.response?.data) {
          const { error: apiError } = error.response.data
          throw new ApiError(
            apiError?.message || 'An error occurred',
            status!,
            apiError?.code,
            apiError?.type
          )
        }

        // Handle network errors (optimized)
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          // Fast offline check
          if (!navigator.onLine) {
            if (isDebugMode) {
              console.log('🌐 Network error - device is offline')
            }
            throw new ApiError('You are currently offline. Please check your connection.', 0, 'OFFLINE')
          }
          throw new ApiError('Network error. Please check your connection.', 0, 'NETWORK_ERROR')
        }

        // Generic error
        throw new ApiError('An unexpected error occurred', status || 500)
      }
    )
  }

  // Generic HTTP Methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data.data as T
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async patch<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.patch<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<ApiResponse<T>>(url)
    return response.data.data as T
  }

  // Get raw response (for cases where you need full response data)
  async getRaw<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data
  }

  async postRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data
  }

  async putRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data
  }

  async patchRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.patch<ApiResponse<T>>(url, data)
    return response.data
  }

  async deleteRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.delete<ApiResponse<T>>(url, { data })
    return response.data
  }
}

// Export singleton instance
export const apiService = new ApiService()
export default apiService
