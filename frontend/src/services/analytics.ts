import { apiService } from './api'

// Consent checking helper
const checkAnalyticsConsent = (): boolean => {
  try {
    // Check sessionStorage first (current session)
    let consentData = sessionStorage.getItem('hlenergy_consent')

    // Fallback to localStorage if not in session
    if (!consentData) {
      consentData = localStorage.getItem('hlenergy_consent')
    }

    if (!consentData) return false

    const consent = JSON.parse(consentData)
    return consent.hasConsented && consent.preferences?.analytics === true
  } catch (error) {
    console.warn('Failed to check analytics consent:', error)
    return false
  }
}

// Analytics Types
export interface UserEvent {
  event: string
  category: 'page_view' | 'user_action' | 'form_interaction' | 'business_event'
  properties?: Record<string, any>
  timestamp?: number
  sessionId?: string
  userId?: string
}

export interface PageView {
  page: string
  title: string
  referrer?: string
  duration?: number
  timestamp: number
}

export interface ConversionEvent {
  type: 'contact_form' | 'registration' | 'login' | 'service_inquiry'
  value?: number
  properties?: Record<string, any>
}

export interface AnalyticsMetrics {
  totalUsers: number
  activeUsers: number
  pageViews: number
  sessions: number
  bounceRate: number
  avgSessionDuration: number
  conversionRate: number
  topPages: Array<{ page: string; views: number }>
  topReferrers: Array<{ referrer: string; visits: number }>
  userFlow: Array<{ from: string; to: string; count: number }>
}

export interface BusinessMetrics {
  totalLeads: number
  qualifiedLeads: number
  conversionRate: number
  leadSources: Array<{ source: string; count: number; conversionRate: number }>
  serviceInterest: Array<{ service: string; inquiries: number }>
  geographicData: Array<{ region: string; leads: number }>
  timeSeriesData: Array<{ date: string; leads: number; conversions: number }>
}

export interface HeatmapData {
  page: string
  clicks: Array<{ x: number; y: number; count: number }>
  scrollDepth: Array<{ depth: number; users: number }>
  timeOnPage: number
}

// Analytics Service
class AnalyticsService {
  private sessionId: string
  private userId?: string
  private eventQueue: UserEvent[] = []
  private isOnline: boolean = navigator.onLine

  constructor() {
    this.sessionId = this.generateSessionId()
    this.initializeAnalytics()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeAnalytics(): void {
    // Load any stored offline events
    this.loadStoredEvents()

    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true
      console.log('Analytics: Back online, flushing queued events')
      this.flushEventQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      console.log('Analytics: Gone offline, events will be queued')
    })

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.flushEventQueue()
      }
    })

    // Track page unload
    window.addEventListener('beforeunload', () => {
      this.flushEventQueue()
    })

    // Set up periodic event flushing
    setInterval(() => this.flushEventQueue(), 30000) // Every 30 seconds
  }

  // Set user ID for authenticated users
  setUserId(userId: string): void {
    this.userId = userId
  }

  // Track page views
  trackPageView(page: string, title: string, referrer?: string): void {
    if (!checkAnalyticsConsent()) {
      console.log('🚫 Page view tracking blocked - no consent')
      return
    }

    const event: UserEvent = {
      event: 'page_view',
      category: 'page_view',
      properties: {
        page,
        title,
        referrer: referrer || document.referrer,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      },
      sessionId: this.sessionId,
      userId: this.userId
    }

    this.queueEvent(event)
  }

  // Track user interactions
  trackEvent(event: string, category: UserEvent['category'], properties?: Record<string, any>): void {
    if (!checkAnalyticsConsent()) {
      console.log('🚫 Event tracking blocked - no consent:', event)
      return
    }

    const analyticsEvent: UserEvent = {
      event,
      category,
      properties: {
        ...properties,
        page: window.location.pathname,
        timestamp: Date.now()
      },
      sessionId: this.sessionId,
      userId: this.userId
    }

    this.queueEvent(analyticsEvent)
  }

  // Track conversions
  trackConversion(type: ConversionEvent['type'], value?: number, properties?: Record<string, any>): void {
    if (!checkAnalyticsConsent()) {
      console.log('🚫 Conversion tracking blocked - no consent:', type)
      return
    }

    this.trackEvent('conversion', 'business_event', {
      conversionType: type,
      value,
      ...properties
    })
  }

  // Track form interactions
  trackFormInteraction(formName: string, action: 'start' | 'complete' | 'abandon', fieldName?: string): void {
    this.trackEvent('form_interaction', 'form_interaction', {
      formName,
      action,
      fieldName
    })
  }

  // Track clicks with coordinates (for heatmaps)
  trackClick(x: number, y: number, element?: string, text?: string): void {
    this.trackEvent('click', 'user_action', {
      x,
      y,
      element,
      text,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    })
  }

  // Track scroll depth
  trackScrollDepth(depth: number): void {
    this.trackEvent('scroll', 'user_action', {
      depth,
      maxDepth: document.documentElement.scrollHeight
    })
  }

  // Queue events for batch sending
  private queueEvent(event: UserEvent): void {
    this.eventQueue.push(event)

    // Auto-flush if queue gets too large or if it's a critical event
    if (this.eventQueue.length >= 10 || event.category === 'business_event') {
      this.flushEventQueue()
    }
  }

  // Send queued events to server
  private async flushEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0 || !this.isOnline) return

    const events = [...this.eventQueue]
    this.eventQueue = []

    try {
      await apiService.post('/analytics/events', { events })
      console.log(`Successfully sent ${events.length} analytics events`)
    } catch (error: any) {
      console.warn('Failed to send analytics events:', error?.message || error)

      // Re-queue events if sending failed, but limit queue size
      const maxQueueSize = 1000
      const eventsToRequeue = events.slice(0, maxQueueSize - this.eventQueue.length)
      this.eventQueue.unshift(...eventsToRequeue)

      // Store events locally for offline persistence
      this.storeEventsLocally(events)

      // If it's a network error, we'll retry when back online
      if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('fetch')) {
        console.log('Network error detected, events will be retried when online')
      }
    }
  }

  private storeEventsLocally(events: AnalyticsEvent[]): void {
    try {
      const existingEvents = JSON.parse(localStorage.getItem('analytics_offline_events') || '[]')
      const allEvents = [...existingEvents, ...events]

      // Keep only last 500 events to prevent storage bloat
      const recentEvents = allEvents.slice(-500)
      localStorage.setItem('analytics_offline_events', JSON.stringify(recentEvents))
    } catch (error) {
      console.warn('Failed to store analytics events locally:', error)
    }
  }

  private loadStoredEvents(): void {
    try {
      const storedEvents = JSON.parse(localStorage.getItem('analytics_offline_events') || '[]')
      if (storedEvents.length > 0) {
        console.log(`Loading ${storedEvents.length} stored analytics events`)
        this.eventQueue.unshift(...storedEvents)
        localStorage.removeItem('analytics_offline_events')
      }
    } catch (error) {
      console.warn('Failed to load stored analytics events:', error)
    }
  }

  // Get analytics metrics
  async getAnalyticsMetrics(timeRange: '24h' | '7d' | '30d' | '90d' = '7d'): Promise<AnalyticsMetrics> {
    return await apiService.get<AnalyticsMetrics>(`/analytics/metrics?timeRange=${timeRange}`)
  }

  // Get business metrics
  async getBusinessMetrics(timeRange: '24h' | '7d' | '30d' | '90d' = '7d'): Promise<BusinessMetrics> {
    return await apiService.get<BusinessMetrics>(`/analytics/business-metrics?timeRange=${timeRange}`)
  }

  // Get heatmap data
  async getHeatmapData(page: string, timeRange: '24h' | '7d' | '30d' = '7d'): Promise<HeatmapData> {
    return await apiService.get<HeatmapData>(`/analytics/heatmap?page=${encodeURIComponent(page)}&timeRange=${timeRange}`)
  }

  // Get conversion funnel data
  async getConversionFunnel(): Promise<Array<{ step: string; users: number; conversionRate: number }>> {
    return await apiService.get('/analytics/conversion-funnel')
  }

  // Get user journey data
  async getUserJourney(userId?: string): Promise<Array<{ page: string; timestamp: number; duration: number }>> {
    const params = userId ? `?userId=${userId}` : ''
    return await apiService.get(`/analytics/user-journey${params}`)
  }

  // A/B Testing
  async getABTestVariant(testName: string): Promise<{ variant: string; config: Record<string, any> }> {
    return await apiService.get(`/analytics/ab-test/${testName}`)
  }

  async trackABTestConversion(testName: string, variant: string): Promise<void> {
    await apiService.post('/analytics/ab-test/conversion', {
      testName,
      variant,
      sessionId: this.sessionId,
      userId: this.userId
    })
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService()
export default analyticsService
