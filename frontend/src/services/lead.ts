import { apiService, type PaginatedResponse } from './api'

// Lead Types
export interface Lead {
  id?: number
  first_name: string
  last_name: string
  email: string
  phone?: string
  company_name?: string
  job_title?: string
  lead_source: 'website' | 'referral' | 'social_media' | 'email_campaign' | 'phone' | 'event' | 'advertisement' | 'partner' | 'other'
  lead_score: number
  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'negotiation' | 'won' | 'lost' | 'nurturing' | 'follow_up' | 'interested' | 'not_interested' | 'callback_requested' | 'meeting_scheduled' | 'demo_scheduled' | 'quote_sent' | 'contract_sent' | 'on_hold' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  address?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  website?: string
  industry?: string
  company_size?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+'
  annual_revenue?: number
  monthly_energy_budget?: number
  current_energy_provider?: string
  energy_needs?: any
  assigned_to?: number
  created_by?: number
  notes?: string
  tags?: string[]
  first_contact_date?: string
  last_contact_date?: string
  next_follow_up_date?: string
  qualified_date?: string
  converted_date?: string
  estimated_value?: number
  actual_value?: number
  contact_submission_id?: number
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  referrer_url?: string
  metadata?: any
  ip_address?: string
  user_agent?: string
  created_at?: string
  updated_at?: string
  
  // Associations
  assignedUser?: {
    id: number
    name: string
    email: string
  }
  createdByUser?: {
    id: number
    name: string
    email: string
  }
  contactSubmission?: {
    id: number
    message: string
    created_at: string
  }
  activities?: LeadActivity[]
}

export interface LeadActivity {
  id?: number
  lead_id: number
  activity_type: 'call' | 'email' | 'meeting' | 'note' | 'task' | 'proposal' | 'quote' | 'demo' | 'follow_up'
  subject?: string
  description?: string
  outcome?: string
  scheduled_at?: string
  completed_at?: string
  duration_minutes?: number
  created_by?: number
  assigned_to?: number
  metadata?: any
  created_at?: string
  updated_at?: string
  
  // Associations
  lead?: Lead
  createdByUser?: {
    id: number
    name: string
    email: string
  }
  assignedUser?: {
    id: number
    name: string
    email: string
  }
}

export interface LeadListParams {
  page?: number
  limit?: number
  status?: string | string[]
  priority?: string | string[]
  lead_source?: string | string[]
  assigned_to?: number
  search?: string
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
  include_activities?: boolean
}

export interface LeadStats {
  total_leads: number
  average_score: number
  status_breakdown: Record<string, number>
  source_breakdown: Record<string, number>
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  message: string
  source?: string
}

// Lead Service
class LeadService {
  // Get leads with filtering and pagination
  async getLeads(params: LeadListParams = {}): Promise<PaginatedResponse<Lead>> {
    const response = await apiService.getRaw<PaginatedResponse<Lead>>('/leads', params)
    return response as any
  }

  // Get specific lead
  async getLead(id: number, includeActivities: boolean = true): Promise<Lead> {
    const response = await apiService.getRaw<{ data: Lead }>(`/leads/${id}`, { include_activities: includeActivities })
    return response.data
  }

  // Create new lead
  async createLead(leadData: Partial<Lead>): Promise<Lead> {
    const response = await apiService.postRaw<{ data: Lead }>('/leads', leadData)
    return response.data
  }

  // Update lead
  async updateLead(id: number, leadData: Partial<Lead>): Promise<Lead> {
    const response = await apiService.putRaw<{ data: Lead }>(`/leads/${id}`, leadData)
    return response.data
  }

  // Delete lead
  async deleteLead(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/leads/${id}`)
  }

  // Create lead from contact submission
  async createLeadFromContact(submissionId: number, additionalData?: any): Promise<Lead> {
    const response = await apiService.postRaw<{ data: Lead }>(`/leads/from-contact/${submissionId}`, additionalData)
    return response.data
  }

  // Get lead statistics
  async getLeadStats(startDate?: string, endDate?: string): Promise<LeadStats> {
    const params: any = {}
    if (startDate) params.start_date = startDate
    if (endDate) params.end_date = endDate
    return await apiService.get<LeadStats>('/leads/stats', params)
  }

  // Bulk update leads
  async bulkUpdateLeads(leadIds: number[], updates: Partial<Lead>): Promise<{
    success: boolean
    data: Array<{ id: number; success: boolean; data?: Lead; error?: string }>
  }> {
    return await apiService.patch('/leads/bulk', {
      lead_ids: leadIds,
      updates
    })
  }

  // Assign lead to user
  async assignLead(id: number, assignedTo: number): Promise<Lead> {
    return await apiService.patch<Lead>(`/leads/${id}/assign`, { assigned_to: assignedTo })
  }

  // Update lead status
  async updateLeadStatus(id: number, status: Lead['status'], notes?: string): Promise<Lead> {
    const data: any = { status }
    if (notes) data.notes = notes
    return await apiService.patch<Lead>(`/leads/${id}/status`, data)
  }

  // Helper method to create lead from contact form data
  async createLeadFromContactForm(contactData: ContactFormData): Promise<Lead> {
    // Split name into first and last name
    const nameParts = contactData.name.trim().split(' ')
    const firstName = nameParts[0] || ''
    const lastName = nameParts.slice(1).join(' ') || ''

    const leadData: Partial<Lead> = {
      first_name: firstName,
      last_name: lastName,
      email: contactData.email,
      phone: contactData.phone,
      lead_source: (contactData.source as Lead['lead_source']) || 'website',
      status: 'new',
      priority: 'medium',
      lead_score: 50, // Default lead score
      notes: `Initial contact message: ${contactData.message}`,
      first_contact_date: new Date().toISOString(),
      next_follow_up_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      metadata: {
        original_message: contactData.message,
        contact_form_submission: true,
        submission_date: new Date().toISOString()
      }
    }

    return await this.createLead(leadData)
  }

  // Get recent leads (for dashboard)
  async getRecentLeads(limit: number = 5): Promise<Lead[]> {
    const response = await this.getLeads({
      limit,
      sortBy: 'created_at',
      sortOrder: 'DESC',
      include_activities: false
    })
    return response.data || response.items || []
  }

  // Get leads needing follow-up
  async getLeadsNeedingFollowUp(limit: number = 20): Promise<Lead[]> {
    const response = await this.getLeads({
      limit,
      sortBy: 'next_follow_up_date',
      sortOrder: 'ASC',
      include_activities: false
    })
    
    // Filter leads that have follow-up dates in the past or today
    const now = new Date()
    return (response.data || response.items || []).filter(lead => 
      lead.next_follow_up_date && new Date(lead.next_follow_up_date) <= now
    )
  }

  // Get high-value leads
  async getHighValueLeads(minScore: number = 70, limit: number = 10): Promise<Lead[]> {
    const response = await this.getLeads({
      limit,
      sortBy: 'lead_score',
      sortOrder: 'DESC',
      include_activities: false
    })
    
    // Filter leads with high scores and not won/lost
    return (response.data || response.items || []).filter(lead => 
      lead.lead_score >= minScore && !['won', 'lost'].includes(lead.status)
    )
  }

  // Format lead display name
  getLeadDisplayName(lead: Lead): string {
    const fullName = `${lead.first_name} ${lead.last_name}`.trim()
    if (lead.company_name) {
      return `${fullName} (${lead.company_name})`
    }
    return fullName
  }

  // Get lead initials
  getLeadInitials(lead: Lead): string {
    const firstInitial = lead.first_name?.charAt(0) || ''
    const lastInitial = lead.last_name?.charAt(0) || ''
    return `${firstInitial}${lastInitial}`.toUpperCase() || 'UN'
  }

  // Format time ago
  formatTimeAgo(dateString: string): string {
    if (!dateString) return 'Unknown'
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  // Get status badge class
  getStatusBadgeClass(status: Lead['status']): string {
    const statusClasses = {
      'new': 'badge-primary',
      'contacted': 'badge-info',
      'qualified': 'badge-success',
      'proposal': 'badge-warning',
      'negotiation': 'badge-warning',
      'won': 'badge-success',
      'lost': 'badge-error',
      'nurturing': 'badge-secondary',
      'follow_up': 'badge-info',
      'interested': 'badge-accent',
      'not_interested': 'badge-neutral',
      'callback_requested': 'badge-info',
      'meeting_scheduled': 'badge-accent',
      'demo_scheduled': 'badge-accent',
      'quote_sent': 'badge-warning',
      'contract_sent': 'badge-warning',
      'on_hold': 'badge-neutral',
      'closed': 'badge-ghost'
    }
    return statusClasses[status] || 'badge-neutral'
  }

  // Get priority badge class
  getPriorityBadgeClass(priority: Lead['priority']): string {
    const priorityClasses = {
      'low': 'badge-neutral',
      'medium': 'badge-info',
      'high': 'badge-warning',
      'urgent': 'badge-error'
    }
    return priorityClasses[priority] || 'badge-neutral'
  }
}

// Export singleton instance
export const leadService = new LeadService()
export default leadService
