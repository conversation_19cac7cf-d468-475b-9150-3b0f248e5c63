import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

export interface NotificationPermission {
  state: 'default' | 'granted' | 'denied'
  supported: boolean
}

export interface PushSubscription {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
}

export function usePushNotifications() {
  const authStore = useAuthStore()

  // State
  const permission = ref<NotificationPermission>({
    state: 'default',
    supported: false
  })
  const subscription = ref<PushSubscription | null>(null)
  const isSubscribing = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const canRequestPermission = computed(() => 
    permission.value.supported && permission.value.state === 'default'
  )
  
  const isGranted = computed(() => 
    permission.value.state === 'granted'
  )
  
  const isDenied = computed(() => 
    permission.value.state === 'denied'
  )

  const isSubscribed = computed(() => 
    subscription.value !== null
  )

  // Methods
  const checkSupport = () => {
    const supported = 'Notification' in window && 
                     'serviceWorker' in navigator && 
                     'PushManager' in window

    permission.value.supported = supported
    
    if (supported) {
      permission.value.state = Notification.permission
    }

    return supported
  }

  const requestPermission = async (): Promise<boolean> => {
    try {
      if (!permission.value.supported) {
        throw new Error('Push notifications are not supported')
      }

      if (permission.value.state === 'granted') {
        return true
      }

      const result = await Notification.requestPermission()
      permission.value.state = result

      if (result === 'granted') {
        console.log('Push notification permission granted')
        return true
      } else {
        console.log('Push notification permission denied')
        return false
      }
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to request notification permission:', err)
      return false
    }
  }

  const subscribe = async (): Promise<boolean> => {
    try {
      if (!permission.value.supported) {
        throw new Error('Push notifications are not supported')
      }

      if (permission.value.state !== 'granted') {
        const granted = await requestPermission()
        if (!granted) {
          throw new Error('Permission not granted')
        }
      }

      isSubscribing.value = true
      error.value = null

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready
      
      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription()
      if (existingSubscription) {
        subscription.value = {
          endpoint: existingSubscription.endpoint,
          keys: {
            p256dh: arrayBufferToBase64(existingSubscription.getKey('p256dh')!),
            auth: arrayBufferToBase64(existingSubscription.getKey('auth')!)
          }
        }
        return true
      }

      // Subscribe to push notifications
      // Note: In production, you'll need to generate VAPID keys
      const vapidPublicKey = 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY'
      
      const pushSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      })

      subscription.value = {
        endpoint: pushSubscription.endpoint,
        keys: {
          p256dh: arrayBufferToBase64(pushSubscription.getKey('p256dh')!),
          auth: arrayBufferToBase64(pushSubscription.getKey('auth')!)
        }
      }

      // Send subscription to server (pass the actual PushSubscription object)
      try {
        await sendSubscriptionToServer(pushSubscription)
        console.log('✅ Subscription sent to server successfully')
      } catch (serverError) {
        console.error('❌ Failed to send subscription to server:', serverError)
        // Don't fail the whole subscription process if server fails
        error.value = `Subscription created locally but server sync failed: ${serverError.message}`
      }

      console.log('Successfully subscribed to push notifications')
      return true

    } catch (err: any) {
      error.value = err.message
      console.error('Failed to subscribe to push notifications:', err)
      return false
    } finally {
      isSubscribing.value = false
    }
  }

  const unsubscribe = async (): Promise<boolean> => {
    try {
      const registration = await navigator.serviceWorker.ready
      const pushSubscription = await registration.pushManager.getSubscription()
      
      if (pushSubscription) {
        await pushSubscription.unsubscribe()
        
        // Remove from server (pass the actual PushSubscription object)
        await removeSubscriptionFromServer(pushSubscription)
        
        subscription.value = null
        console.log('Successfully unsubscribed from push notifications')
        return true
      }
      
      return false
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to unsubscribe from push notifications:', err)
      return false
    }
  }

  const showNotification = async (title: string, options?: NotificationOptions) => {
    try {
      if (!isGranted.value) {
        throw new Error('Notification permission not granted')
      }

      const registration = await navigator.serviceWorker.ready
      await registration.showNotification(title, {
        icon: '/hl-energy-logo-192w.png',
        badge: '/hl-energy-logo-96w.png',
        ...options
      })
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to show notification:', err)
    }
  }

  const sendTestNotification = async (message?: string) => {
    try {
      if (!authStore.token) {
        throw new Error('Authentication required to send notifications')
      }

      console.log('🔔 Sending test notification...')
      console.log('🔑 Auth token:', authStore.token ? 'Present' : 'Missing')
      console.log('👤 User:', authStore.user)

      const response = await fetch(`${API_BASE_URL}/api/v1/push/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          message: message || 'This is a test notification from HLenergy! 🔋'
        })
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ Test notification sent successfully:', result)

      // Also show a local notification to verify it's working
      if (Notification.permission === 'granted') {
        console.log('🔔 Showing local test notification as well...')
        await showNotification('Test Notification Sent!', {
          body: 'The server notification was sent successfully. You should see both notifications.',
          tag: 'test-confirmation',
          requireInteraction: false,
          icon: '/hl-energy-logo-192w.png',
          badge: '/hl-energy-logo-96w.png'
        })
      } else {
        console.warn('⚠️ Notification permission not granted, cannot show local notification')
        console.log('Current permission:', Notification.permission)
      }

      // Clear any previous errors
      error.value = null

      return result
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send test notification'
      error.value = errorMessage
      console.error('❌ Failed to send test notification:', err)
      throw new Error(errorMessage)
    }
  }

  // Helper functions
  const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  const urlBase64ToUint8Array = (base64String: string): Uint8Array => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  const sendSubscriptionToServer = async (sub: PushSubscription) => {
    try {
      if (!authStore.token) {
        console.warn('⚠️ No auth token available, skipping server subscription')
        return
      }

      console.log('📤 Sending subscription to server...')
      console.log('🔍 Auth token available:', !!authStore.token)
      console.log('🔍 User data:', authStore.user)

      const subscriptionData = {
        subscription: {
          endpoint: sub.endpoint,
          keys: {
            p256dh: arrayBufferToBase64(sub.getKey('p256dh')!),
            auth: arrayBufferToBase64(sub.getKey('auth')!)
          }
        },
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          deviceType: 'web'
        }
      }

      console.log('📦 Subscription data:', {
        endpoint: subscriptionData.subscription.endpoint.substring(0, 50) + '...',
        hasP256dh: !!subscriptionData.subscription.keys.p256dh,
        hasAuth: !!subscriptionData.subscription.keys.auth,
        deviceInfo: subscriptionData.deviceInfo
      })

      const response = await fetch(`${API_BASE_URL}/api/v1/push/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(subscriptionData)
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        console.error('❌ Server error response:', errorData)
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ Subscription saved to server:', result)
    } catch (err) {
      console.error('❌ Failed to send subscription to server:', err)
      // Don't throw here, subscription still works locally
    }
  }

  const removeSubscriptionFromServer = async (sub: PushSubscription) => {
    try {
      await fetch(`${API_BASE_URL}/api/v1/push/unsubscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          removeAll: true // Remove all subscriptions for this user
        })
      })
      console.log('✅ Subscription removed from server')
    } catch (err) {
      console.error('Failed to remove subscription from server:', err)
    }
  }

  // Initialize
  const initialize = () => {
    checkSupport()
    
    if (permission.value.supported) {
      // Check for existing subscription
      navigator.serviceWorker.ready.then(async (registration) => {
        const existingSubscription = await registration.pushManager.getSubscription()
        if (existingSubscription) {
          subscription.value = {
            endpoint: existingSubscription.endpoint,
            keys: {
              p256dh: arrayBufferToBase64(existingSubscription.getKey('p256dh')!),
              auth: arrayBufferToBase64(existingSubscription.getKey('auth')!)
            }
          }
        }
      })
    }
  }

  // Auto-initialize on mount
  onMounted(() => {
    initialize()
  })

  return {
    // State
    permission,
    subscription,
    isSubscribing,
    error,

    // Computed
    canRequestPermission,
    isGranted,
    isDenied,
    isSubscribed,

    // Methods
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification,
    sendTestNotification,
    initialize
  }
}
