/**
 * Vue Composable for Performance Tracking
 * Easy-to-use performance monitoring for Vue components
 */

import { onMounted, onUnmounted, onUpdated, ref, watch, type Ref } from 'vue'
import { performanceProfiler } from '@/utils/performanceProfiler'

interface PerformanceOptions {
  trackMounting?: boolean
  trackUpdates?: boolean
  trackProps?: boolean
  trackAsyncOperations?: boolean
  customMetrics?: boolean
}

export function usePerformance(
  componentName: string, 
  options: PerformanceOptions = {}
) {
  const {
    trackMounting = true,
    trackUpdates = true,
    trackProps = false,
    trackAsyncOperations = true,
    customMetrics = true
  } = options

  const isEnabled = ref(import.meta.env.DEV || localStorage.getItem('enable-profiler') === 'true')
  const mountTime = ref<number>(0)
  const updateCount = ref<number>(0)
  const lastUpdateTime = ref<number>(0)
  const asyncOperations = ref<Map<string, number>>(new Map())

  // Track component mounting
  if (trackMounting && isEnabled.value) {
    onMounted(() => {
      mountTime.value = performance.now()
      performanceProfiler.startComponentTimer(componentName)
      performanceProfiler.addCustomMetric(`${componentName} Mounted`, mountTime.value)
    })
  }

  // Track component updates
  if (trackUpdates && isEnabled.value) {
    onUpdated(() => {
      updateCount.value++
      lastUpdateTime.value = performance.now()
      performanceProfiler.addCustomMetric(`${componentName} Updated`, lastUpdateTime.value, {
        updateCount: updateCount.value
      })
    })
  }

  // Cleanup on unmount
  onUnmounted(() => {
    if (isEnabled.value) {
      performanceProfiler.endComponentTimer(componentName)
      const totalTime = performance.now() - mountTime.value
      performanceProfiler.addCustomMetric(`${componentName} Unmounted`, totalTime, {
        totalLifetime: totalTime,
        updateCount: updateCount.value
      })
    }
  })

  // Track prop changes
  const trackPropChange = (propName: string, newValue: any, oldValue: any) => {
    if (!trackProps || !isEnabled.value) return
    
    performanceProfiler.addCustomMetric(`${componentName} Prop Changed`, performance.now(), {
      propName,
      hasNewValue: newValue !== undefined,
      hasOldValue: oldValue !== undefined,
      valueChanged: newValue !== oldValue
    })
  }

  // Track async operations
  const trackAsyncStart = (operationName: string): string => {
    if (!trackAsyncOperations || !isEnabled.value) return ''
    
    const operationId = `${componentName}-${operationName}-${Date.now()}`
    asyncOperations.value.set(operationId, performance.now())
    return operationId
  }

  const trackAsyncEnd = (operationId: string, success: boolean = true, error?: Error) => {
    if (!trackAsyncOperations || !isEnabled.value || !operationId) return
    
    const startTime = asyncOperations.value.get(operationId)
    if (startTime) {
      const duration = performance.now() - startTime
      asyncOperations.value.delete(operationId)
      
      performanceProfiler.addCustomMetric(`${componentName} Async Operation`, duration, {
        operationId,
        success,
        error: error?.message,
        duration
      })
    }
  }

  // Custom metric tracking
  const addMetric = (metricName: string, value: number, details?: any) => {
    if (!customMetrics || !isEnabled.value) return
    
    performanceProfiler.addCustomMetric(`${componentName} ${metricName}`, value, details)
  }

  // Measure function execution time
  const measureFunction = async <T>(
    fn: () => T | Promise<T>, 
    functionName: string
  ): Promise<T> => {
    if (!isEnabled.value) {
      return typeof fn === 'function' ? await fn() : fn
    }

    const startTime = performance.now()
    try {
      const result = await fn()
      const duration = performance.now() - startTime
      addMetric(`Function ${functionName}`, duration, { success: true })
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      addMetric(`Function ${functionName}`, duration, { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  // Track render performance
  const trackRender = (renderName: string = 'render') => {
    if (!isEnabled.value) return { start: () => {}, end: () => {} }

    let startTime: number

    return {
      start: () => {
        startTime = performance.now()
      },
      end: () => {
        if (startTime) {
          const duration = performance.now() - startTime
          addMetric(`Render ${renderName}`, duration)
        }
      }
    }
  }

  // Watch for performance issues
  const performanceIssues = ref<string[]>([])
  
  watch([updateCount, lastUpdateTime], ([newUpdateCount, newLastUpdateTime]) => {
    if (!isEnabled.value) return

    // Check for excessive updates
    if (newUpdateCount > 50) {
      const issue = `${componentName}: Excessive updates detected (${newUpdateCount})`
      if (!performanceIssues.value.includes(issue)) {
        performanceIssues.value.push(issue)
        console.warn('⚠️ Performance Issue:', issue)
      }
    }

    // Check for slow updates
    const updateDuration = newLastUpdateTime - (mountTime.value || 0)
    if (updateDuration > 16) { // Slower than 60fps
      const issue = `${componentName}: Slow update detected (${updateDuration.toFixed(2)}ms)`
      if (!performanceIssues.value.includes(issue)) {
        performanceIssues.value.push(issue)
        console.warn('⚠️ Performance Issue:', issue)
      }
    }
  })

  // Memory usage tracking
  const trackMemoryUsage = () => {
    if (!isEnabled.value || !performance.memory) return null

    const memory = performance.memory
    const memoryInfo = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
    }

    addMetric('Memory Usage', memoryInfo.used, memoryInfo)
    return memoryInfo
  }

  // Network request tracking
  const trackNetworkRequest = (url: string, method: string = 'GET') => {
    if (!isEnabled.value) return { start: () => {}, end: () => {} }

    let startTime: number

    return {
      start: () => {
        startTime = performance.now()
      },
      end: (success: boolean = true, statusCode?: number, responseSize?: number) => {
        if (startTime) {
          const duration = performance.now() - startTime
          addMetric('Network Request', duration, {
            url,
            method,
            success,
            statusCode,
            responseSize,
            duration
          })
        }
      }
    }
  }

  // Bundle size impact tracking
  const trackBundleImpact = (featureName: string, estimatedSize: number) => {
    if (!isEnabled.value) return

    addMetric('Bundle Impact', estimatedSize, {
      feature: featureName,
      component: componentName,
      estimatedKB: estimatedSize
    })
  }

  // Performance summary for component
  const getPerformanceSummary = () => {
    if (!isEnabled.value) return null

    return {
      componentName,
      mountTime: mountTime.value,
      updateCount: updateCount.value,
      lastUpdateTime: lastUpdateTime.value,
      activeAsyncOperations: asyncOperations.value.size,
      performanceIssues: performanceIssues.value,
      isEnabled: isEnabled.value
    }
  }

  // Enable/disable profiling
  const enableProfiling = () => {
    isEnabled.value = true
    performanceProfiler.enable()
  }

  const disableProfiling = () => {
    isEnabled.value = false
    performanceProfiler.disable()
  }

  return {
    // State
    isEnabled,
    mountTime,
    updateCount,
    lastUpdateTime,
    performanceIssues,

    // Methods
    trackPropChange,
    trackAsyncStart,
    trackAsyncEnd,
    addMetric,
    measureFunction,
    trackRender,
    trackMemoryUsage,
    trackNetworkRequest,
    trackBundleImpact,
    getPerformanceSummary,
    enableProfiling,
    disableProfiling
  }
}

// Utility function for tracking reactive data performance
export function useReactivePerformance<T>(
  data: Ref<T>,
  dataName: string,
  componentName: string
) {
  const { addMetric, isEnabled } = usePerformance(componentName, { customMetrics: true })
  
  let lastSize = 0
  let changeCount = 0

  watch(data, (newValue) => {
    if (!isEnabled.value) return

    changeCount++
    
    // Estimate data size
    const currentSize = JSON.stringify(newValue).length
    const sizeChange = currentSize - lastSize
    
    addMetric(`Reactive Data ${dataName}`, currentSize, {
      changeCount,
      sizeChange,
      currentSize,
      dataType: typeof newValue
    })

    // Warn about large data changes
    if (Math.abs(sizeChange) > 10000) { // 10KB change
      console.warn(`⚠️ Large data change in ${componentName}.${dataName}: ${sizeChange} bytes`)
    }

    lastSize = currentSize
  }, { deep: true })

  return {
    changeCount: ref(changeCount),
    currentSize: ref(lastSize)
  }
}

export default usePerformance
