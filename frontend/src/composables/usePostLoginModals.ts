import { ref, computed } from 'vue'
import { useFirebaseMessaging } from './useFirebaseMessaging'

export function usePostLoginModals() {
  // State
  const showPushModal = ref(false)

  // Composables
  const { isPermissionGranted, isSubscribed } = useFirebaseMessaging()

  // Computed
  const shouldShowPushModal = computed(() =>
    !isSubscribed.value  // Show if not subscribed, regardless of permission status
  )

  // Methods
  const startPostLoginFlow = async () => {
    console.log('🚀 Starting post-login Firebase notifications flow...')

    // Wait a bit for Firebase to initialize and check subscription
    await new Promise(resolve => setTimeout(resolve, 500))

    // Debug information
    console.log('📊 Post-login Firebase flow conditions:', {
      shouldShowPushModal: shouldShowPushModal.value,
      isPermissionGranted: isPermissionGranted.value,
      isSubscribed: isSubscribed.value,
      notificationPermission: Notification?.permission || 'not-supported'
    })

    // Check if we need to show Firebase push notifications modal
    if (shouldShowPushModal.value) {
      console.log('🔥 Showing Firebase push notifications modal')
      setTimeout(() => {
        showPushModal.value = true
      }, 100)
    } else {
      console.log('✅ No modal needed - user already has Firebase notifications enabled')
    }
  }

  const handlePushEnabled = () => {
    console.log('✅ Firebase push notifications enabled')
    showPushModal.value = false
  }

  const handlePushDismissed = () => {
    console.log('❌ Firebase push notifications modal dismissed')
    showPushModal.value = false
  }

  const closePushModal = () => {
    showPushModal.value = false
  }

  // Reset flow
  const resetFlow = () => {
    showPushModal.value = false
  }

  return {
    // State
    showPushModal,

    // Computed
    shouldShowPushModal,

    // Methods
    startPostLoginFlow,
    handlePushEnabled,
    handlePushDismissed,
    closePushModal,
    resetFlow
  }
}
