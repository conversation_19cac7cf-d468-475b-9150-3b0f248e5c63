import { onMounted, onUnmounted } from 'vue'

/**
 * Composable for handling automatic data refresh after session unlock
 * Components can use this to automatically refresh their data when session is unlocked
 * The system will automatically try data refresh first, then fallback to page refresh if needed
 */
export function useSessionUnlockRefresh(refreshCallback?: () => void | Promise<void>) {
  let sessionUnlockedListener: ((event: CustomEvent) => void) | null = null
  let adminRefreshListener: (() => void) | null = null
  let dashboardRefreshListener: (() => void) | null = null

  const handleSessionUnlocked = async (event: CustomEvent) => {
    console.log('🔓 Session unlocked event received:', event.detail)

    // Only execute callback if this is a data refresh strategy
    // If it's a page refresh strategy, the page will reload anyway
    if (event.detail?.refreshStrategy === 'page') {
      console.log('📄 Page refresh strategy detected - skipping callback (page will reload)')
      return
    }

    if (refreshCallback) {
      try {
        console.log('🔄 Executing data refresh callback...')
        await refreshCallback()
        console.log('✅ Data refresh callback completed')
      } catch (error) {
        console.error('❌ Error in data refresh callback:', error)
        // If callback fails, we might want to trigger page refresh as fallback
        console.log('⚠️ Data refresh callback failed, page refresh may be triggered as fallback')
      }
    }
  }

  const handleAdminRefresh = async () => {
    console.log('🔧 Admin data refresh event received')
    
    if (refreshCallback) {
      try {
        console.log('🔄 Executing admin refresh callback...')
        await refreshCallback()
        console.log('✅ Admin refresh callback completed')
      } catch (error) {
        console.error('❌ Error in admin refresh callback:', error)
      }
    }
  }

  const handleDashboardRefresh = async () => {
    console.log('📊 Dashboard data refresh event received')
    
    if (refreshCallback) {
      try {
        console.log('🔄 Executing dashboard refresh callback...')
        await refreshCallback()
        console.log('✅ Dashboard refresh callback completed')
      } catch (error) {
        console.error('❌ Error in dashboard refresh callback:', error)
      }
    }
  }

  const setupListeners = () => {
    // General session unlock listener
    sessionUnlockedListener = handleSessionUnlocked
    window.addEventListener('session-unlocked', sessionUnlockedListener as EventListener)

    // Route-specific listeners
    adminRefreshListener = handleAdminRefresh
    window.addEventListener('admin-data-refresh', adminRefreshListener)

    dashboardRefreshListener = handleDashboardRefresh
    window.addEventListener('dashboard-data-refresh', dashboardRefreshListener)

    console.log('🎧 Session unlock refresh listeners set up')
  }

  const cleanupListeners = () => {
    if (sessionUnlockedListener) {
      window.removeEventListener('session-unlocked', sessionUnlockedListener as EventListener)
      sessionUnlockedListener = null
    }

    if (adminRefreshListener) {
      window.removeEventListener('admin-data-refresh', adminRefreshListener)
      adminRefreshListener = null
    }

    if (dashboardRefreshListener) {
      window.removeEventListener('dashboard-data-refresh', dashboardRefreshListener)
      dashboardRefreshListener = null
    }

    console.log('🧹 Session unlock refresh listeners cleaned up')
  }

  onMounted(() => {
    setupListeners()
  })

  onUnmounted(() => {
    cleanupListeners()
  })

  // Return methods for manual control if needed
  return {
    setupListeners,
    cleanupListeners,
    // Trigger refresh manually
    triggerRefresh: refreshCallback
  }
}

/**
 * Composable for components that prefer page refresh after session unlock
 * This is now mainly for compatibility - the system automatically handles refresh strategy
 */
export function usePageRefreshOnUnlock(delay: number = 1000) {
  // The system now automatically handles page refresh as fallback
  // This composable is kept for compatibility but doesn't need to do much
  console.log('📄 Component registered for automatic page refresh handling')

  return useSessionUnlockRefresh(() => {
    // This callback will only run if data refresh strategy is used
    // If page refresh is needed, it will be handled automatically by the auth store
    console.log('📄 Data refresh attempted, but page refresh may still occur as fallback')
  })
}

/**
 * Composable for specific data refresh after session unlock
 * Use this when you want to refresh specific data without full page reload
 */
export function useDataRefreshOnUnlock(refreshFunction: () => void | Promise<void>) {
  return useSessionUnlockRefresh(refreshFunction)
}

/**
 * Manual trigger for session unlock refresh (for testing)
 * This simulates the automatic smart refresh behavior
 */
export function triggerSessionUnlockRefresh() {
  console.log('🔄 Manually triggering automatic session unlock refresh...')

  // Simulate the smart refresh decision
  const currentRoute = window.location.pathname
  const supportsDataRefresh = ['/admin', '/dashboard', '/profile', '/settings'].some(route =>
    currentRoute.includes(route)
  )

  if (supportsDataRefresh) {
    console.log('✅ Simulating data refresh strategy...')
    window.dispatchEvent(new CustomEvent('session-unlocked', {
      detail: {
        timestamp: new Date().toISOString(),
        manual: true,
        refreshStrategy: 'data'
      }
    }))

    // Trigger route-specific events
    if (currentRoute.includes('/admin')) {
      window.dispatchEvent(new CustomEvent('admin-data-refresh'))
    } else if (currentRoute.includes('/dashboard')) {
      window.dispatchEvent(new CustomEvent('dashboard-data-refresh'))
    }
  } else {
    console.log('📄 Simulating page refresh strategy...')
    window.dispatchEvent(new CustomEvent('session-unlocked', {
      detail: {
        timestamp: new Date().toISOString(),
        manual: true,
        refreshStrategy: 'page'
      }
    }))

    // In real scenario, page would refresh after delay
    console.log('🔄 Page refresh would occur after delay...')
  }
}
