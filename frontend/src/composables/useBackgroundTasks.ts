/**
 * Composable for managing background tasks with automatic pause/resume
 * based on page visibility to reduce CPU usage when page is hidden
 */

import { ref, onMounted, onUnmounted } from 'vue'

interface BackgroundTask {
  id: string
  callback: () => void | Promise<void>
  interval: number
  intervalId?: number
  isActive: boolean
  pauseWhenHidden: boolean
  // Removed: lastRun, runCount (memory tracking overhead)
}

export function useBackgroundTasks() {
  const tasks = ref(new Map<string, BackgroundTask>())
  const isPageVisible = ref(!document.hidden)
  const isPaused = ref(false)

  // Hard limits for task registration to prevent memory issues
  const MAX_TASKS = 10 // Hard limit - prevents unlimited task accumulation
  const TASK_WARNING_THRESHOLD = 7 // Warning before hitting limit

  /**
   * Register a background task with hard limits
   */
  const registerTask = (
    id: string,
    callback: () => void | Promise<void>,
    interval: number,
    options: {
      pauseWhenHidden?: boolean
      startImmediately?: boolean
      force?: boolean // Allow bypassing limit for critical tasks
    } = {}
  ) => {
    const { pauseWhenHidden = true, startImmediately = true, force = false } = options

    // Enforce hard limit on task registration
    const currentTaskCount = tasks.value.size

    if (currentTaskCount >= MAX_TASKS && !force) {
      const errorMsg = `🚫 REJECTED: Maximum background tasks limit reached (${currentTaskCount}/${MAX_TASKS}). Task '${id}' not registered.`
      console.error(errorMsg)
      console.error('🔍 Current tasks:', Array.from(tasks.value.keys()))
      console.error('💡 Unregister unused tasks or use force=true for critical tasks')
      throw new Error(`Background task limit exceeded: ${currentTaskCount}/${MAX_TASKS}`)
    }

    if (currentTaskCount >= TASK_WARNING_THRESHOLD) {
      console.warn(`⚠️ WARNING: Approaching task limit (${currentTaskCount}/${MAX_TASKS}). Consider consolidating tasks.`)
      console.warn('🔍 Current tasks:', Array.from(tasks.value.keys()))
    }

    // Clear existing task if it exists
    if (tasks.value.has(id)) {
      unregisterTask(id)
    }

    const task: BackgroundTask = {
      id,
      callback,
      interval: Math.max(interval, 5000), // Minimum 5 seconds
      isActive: false,
      pauseWhenHidden
    }

    tasks.value.set(id, task)

    if (startImmediately) {
      startTask(id)
    }

    console.log(`📋 Background task registered: ${id} (${task.interval}ms) - Total: ${tasks.value.size}/${MAX_TASKS}`)
    return id
  }

  /**
   * Start a specific task
   */
  const startTask = (id: string) => {
    const task = tasks.value.get(id)
    if (!task || task.isActive) return

    // Don't start if page is hidden and task should pause
    if (!isPageVisible.value && task.pauseWhenHidden && !isPaused.value) {
      console.log(`⏸️ Task ${id} not started - page is hidden`)
      return
    }

    const wrappedCallback = async () => {
      try {
        await task.callback()
      } catch (error) {
        console.error(`❌ Background task ${id} failed:`, error)
      }
    }

    task.intervalId = setInterval(wrappedCallback, task.interval)
    task.isActive = true

    console.log(`▶️ Background task started: ${id} (interval: ${task.interval}ms)`)
  }

  /**
   * Stop a specific task
   */
  const stopTask = (id: string) => {
    const task = tasks.value.get(id)
    if (!task || !task.isActive) return

    if (task.intervalId) {
      clearInterval(task.intervalId)
      task.intervalId = undefined
    }
    task.isActive = false

    console.log(`⏹️ Background task stopped: ${id}`)
  }

  /**
   * Unregister a task completely
   */
  const unregisterTask = (id: string) => {
    stopTask(id)
    tasks.value.delete(id)
    console.log(`🗑️ Background task unregistered: ${id}`)
  }

  /**
   * Pause all tasks
   */
  const pauseAllTasks = () => {
    if (isPaused.value) return

    isPaused.value = true
    for (const [id, task] of tasks.value) {
      if (task.isActive) {
        stopTask(id)
      }
    }
    console.log('⏸️ All background tasks paused')
  }

  /**
   * Resume all tasks
   */
  const resumeAllTasks = () => {
    if (!isPaused.value) return

    isPaused.value = false
    for (const [id, task] of tasks.value) {
      if (!task.isActive && (isPageVisible.value || !task.pauseWhenHidden)) {
        startTask(id)
      }
    }
    console.log('▶️ All background tasks resumed')
  }

  /**
   * Handle page visibility changes
   */
  const handleVisibilityChange = () => {
    const wasVisible = isPageVisible.value
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && !wasVisible) {
      // Page became visible
      console.log('👁️ Page became visible - resuming background tasks')
      for (const [id, task] of tasks.value) {
        if (!task.isActive && task.pauseWhenHidden && !isPaused.value) {
          startTask(id)
        }
      }
    } else if (!isPageVisible.value && wasVisible) {
      // Page became hidden
      console.log('🙈 Page became hidden - pausing background tasks')
      for (const [id, task] of tasks.value) {
        if (task.isActive && task.pauseWhenHidden) {
          stopTask(id)
        }
      }
    }
  }

  /**
   * Get task statistics
   */
  const getTaskStats = () => {
    let active = 0
    let paused = 0
    let hidden = 0

    for (const [id, task] of tasks.value) {
      if (task.isActive) {
        active++
      } else if (!isPageVisible.value && task.pauseWhenHidden) {
        hidden++
      } else {
        paused++
      }
    }

    return {
      total: tasks.value.size,
      maxTasks: MAX_TASKS,
      active,
      paused,
      hidden,
      taskIds: Array.from(tasks.value.keys()) // Simple list of task IDs only
    }
  }

  /**
   * Get warning level based on task count
   */
  const getWarningLevel = (taskCount: number): 'normal' | 'warning' | 'critical' => {
    if (taskCount >= MAX_TASKS) return 'critical'
    if (taskCount >= TASK_WARNING_THRESHOLD) return 'warning'
    return 'normal'
  }

  /**
   * Simple task analysis (reduced complexity)
   */
  const printTaskAnalysis = () => {
    const stats = getTaskStats()
    const warningLevel = getWarningLevel(stats.total)

    console.group('📋 Background Task Summary')
    console.log(`📊 Tasks: ${stats.total}/${stats.maxTasks} (${stats.active} active, ${stats.paused} paused, ${stats.hidden} hidden)`)
    console.log(`🚨 Status: ${warningLevel}`)
    console.log(`📝 Task IDs: ${stats.taskIds.join(', ')}`)
    console.groupEnd()

    return { ...stats, warningLevel }
  }

  /**
   * Emergency cleanup - stop all tasks
   */
  const emergencyCleanup = () => {
    console.warn('🚨 Emergency cleanup: stopping all background tasks')
    for (const id of tasks.value.keys()) {
      unregisterTask(id)
    }
  }

  // Setup lifecycle hooks
  onMounted(() => {
    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Listen for page unload to cleanup
    window.addEventListener('beforeunload', emergencyCleanup)

    console.log('📋 Background task manager initialized')
  })

  onUnmounted(() => {
    // Cleanup all tasks
    emergencyCleanup()

    // Remove event listeners
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('beforeunload', emergencyCleanup)

    console.log('📋 Background task manager cleaned up')
  })

  return {
    // State
    isPageVisible,
    isPaused,
    
    // Methods
    registerTask,
    startTask,
    stopTask,
    unregisterTask,
    pauseAllTasks,
    resumeAllTasks,
    getTaskStats,
    getWarningLevel,
    printTaskAnalysis,
    emergencyCleanup
  }
}

// Create a global instance for shared use
let globalBackgroundTasks: ReturnType<typeof useBackgroundTasks> | null = null

export function getGlobalBackgroundTasks() {
  if (!globalBackgroundTasks) {
    globalBackgroundTasks = useBackgroundTasks()
  }
  return globalBackgroundTasks
}

export default useBackgroundTasks
