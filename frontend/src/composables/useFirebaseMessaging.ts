import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

// Global singleton state - shared across all components
const globalFirebaseState = {
  isSupported: ref(true), // Assume supported until proven otherwise
  isPermissionGranted: ref(false),
  isSubscribed: ref(false),
  currentToken: ref<string | null>(null),
  error: ref<string | null>(null),
  isLoading: ref(false),
  isInitialized: ref(false),
  messaging: null as any,
  initializationPromise: null as Promise<any> | null
}

// Firebase messaging composable for push notifications
export function useFirebaseMessaging() {
  const authStore = useAuthStore()

  // Use global singleton state
  const isSupported = globalFirebaseState.isSupported
  const isPermissionGranted = globalFirebaseState.isPermissionGranted
  const isSubscribed = globalFirebaseState.isSubscribed
  const currentToken = globalFirebaseState.currentToken
  const error = globalFirebaseState.error
  const isLoading = globalFirebaseState.isLoading
  const isInitialized = globalFirebaseState.isInitialized

  // Computed
  const canSubscribe = computed(() =>
    isSupported.value && isPermissionGranted.value && !isSubscribed.value
  )

  const permission = computed(() =>
    isPermissionGranted.value ? 'granted' : 'default'
  )

  // Firebase messaging instance (shared singleton)
  let messaging = globalFirebaseState.messaging

  /**
   * Initialize Firebase messaging
   */
  const initializeMessaging = async () => {
    try {
      // Check if service worker and push messaging are supported
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service Worker is not supported in this browser')
      }

      if (!('PushManager' in window)) {
        throw new Error('Push messaging is not supported in this browser')
      }

      console.log('🔥 Browser supports service workers and push messaging')

      // Dynamic import Firebase messaging
      console.log('🔥 Importing Firebase modules...')
      const { getMessaging, getToken, onMessage } = await import('firebase/messaging')
      const { initializeApp } = await import('firebase/app')
      console.log('✅ Firebase modules imported successfully')

      // Firebase config
      const firebaseConfig = {
        apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
        authDomain: "hlenergy-notifications.firebaseapp.com",
        projectId: "hlenergy-notifications",
        storageBucket: "hlenergy-notifications.firebasestorage.app",
        messagingSenderId: "506206785168",
        appId: "1:506206785168:web:5acaeacce5178fd2d45215",
        measurementId: "G-JEMVPQGQ5R"
      }

      // Initialize Firebase app
      console.log('🔥 Initializing Firebase app...')
      const app = initializeApp(firebaseConfig)
      console.log('✅ Firebase app initialized')

      // Initialize messaging
      console.log('🔥 Initializing Firebase messaging...')
      messaging = getMessaging(app)
      globalFirebaseState.messaging = messaging
      console.log('✅ Firebase messaging initialized')

      isSupported.value = true

      // Set up foreground message handler
      onMessage(messaging, (payload) => {
        console.log('🔥 [FCM] ===== FOREGROUND MESSAGE RECEIVED =====')
        console.log('🔥 [FCM] Full payload:', JSON.stringify(payload, null, 2))
        console.log('🔥 [FCM] App is in foreground, showing notification manually')

        // Show notification even when app is in foreground
        const notificationTitle = payload.notification?.title || payload.data?.title || 'HLenergy Notification'
        const notificationOptions = {
          body: payload.notification?.body || payload.data?.body || 'You have a new notification',
          icon: payload.notification?.icon || payload.data?.icon || '/hl-energy-logo-192w.png',
          badge: payload.notification?.badge || payload.data?.badge || '/hl-energy-logo-96w.png',
          tag: payload.data?.tag || 'hlenergy-notification',
          data: payload.data || {},
          requireInteraction: payload.data?.requireInteraction === 'true'
        }

        console.log('🔥 [FCM] Foreground notification:', notificationTitle, notificationOptions)

        // Show notification using Notification API for foreground
        if (Notification.permission === 'granted') {
          console.log('🔥 [FCM] Creating foreground notification...')
          const notification = new Notification(notificationTitle, notificationOptions)

          notification.onclick = () => {
            console.log('🔥 [FCM] Foreground notification clicked')
            window.focus()
            notification.close()
          }

          console.log('✅ [FCM] Foreground notification created')
        } else {
          console.error('❌ [FCM] No notification permission for foreground message')
        }
      })

      return messaging
    } catch (err) {
      console.error('🔥 Firebase messaging initialization failed:', err)
      error.value = err instanceof Error ? err.message : 'Failed to initialize messaging'
      throw err
    }
  }

  /**
   * Request notification permission
   */
  const requestPermission = async () => {
    try {
      console.log('🔥 Requesting notification permission...')
      const permission = await Notification.requestPermission()
      isPermissionGranted.value = permission === 'granted'

      console.log(`🔥 Permission result: ${permission}`)

      if (permission === 'denied') {
        error.value = 'Notification permission was denied. Please enable notifications in your browser settings.'
        console.warn('🔥 Permission denied - user must enable in browser settings')
        return false
      }

      if (permission === 'granted') {
        error.value = null
        console.log('✅ Permission granted successfully')
        return true
      }

      // Permission is 'default' - user dismissed the prompt
      console.log('⚠️ Permission prompt dismissed')
      return false
    } catch (err) {
      console.error('❌ Permission request failed:', err)
      error.value = err instanceof Error ? err.message : 'Permission request failed'
      return false
    }
  }

  /**
   * Get FCM token and subscribe to notifications
   */
  const subscribe = async () => {
    if (isLoading.value) return false
    
    isLoading.value = true
    error.value = null

    try {
      console.log('🔥 Starting FCM subscription...')

      // Check permission first
      if (Notification.permission !== 'granted') {
        const granted = await requestPermission()
        if (!granted) {
          throw new Error('Notification permission is required')
        }
      }

      // Initialize messaging if not already done
      if (!messaging) {
        await initializeMessaging()
      }

      // Get FCM token using the main service worker
      const { getToken } = await import('firebase/messaging')

      // Register the dedicated Firebase messaging service worker
      console.log('🔥 Registering Firebase messaging service worker...')
      const swRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
        scope: '/firebase-cloud-messaging-push-scope'
      })

      console.log('✅ Firebase service worker registered:', swRegistration.scope)

      // Wait for the service worker to be ready
      await navigator.serviceWorker.ready
      console.log('✅ Firebase service worker is ready')

      // Ensure the service worker is active
      if (!swRegistration.active && !swRegistration.installing) {
        throw new Error('Firebase service worker failed to activate')
      }

      console.log('🔥 Getting FCM token with Firebase service worker...')
      console.log('🔥 Service worker scope:', swRegistration.scope)
      console.log('🔥 Service worker active:', !!swRegistration.active)
      console.log('🔥 Service worker installing:', !!swRegistration.installing)

      const token = await getToken(messaging, {
        vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY',
        serviceWorkerRegistration: swRegistration
      })

      console.log('🔥 FCM token details:', {
        tokenLength: token?.length,
        tokenStart: token?.substring(0, 20),
        tokenEnd: token?.substring(token.length - 20)
      })

      if (!token) {
        throw new Error('Failed to get FCM token')
      }

      console.log('🔥 FCM token obtained:', token.substring(0, 20) + '...')
      currentToken.value = token

      // Send token to backend
      await sendTokenToServer(token)

      isSubscribed.value = true

      // Store subscription state in localStorage as backup
      localStorage.setItem('fcm-subscribed', 'true')
      localStorage.setItem('fcm-token', token)

      console.log('✅ FCM subscription successful')

      return true
    } catch (err) {
      console.error('❌ FCM subscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Subscription failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Send FCM token to backend
   */
  const sendTokenToServer = async (token: string) => {
    try {
      console.log('📤 Sending FCM token to server...')

      const response = await fetch(`${API_BASE_URL}/api/v1/push/subscribe/fcm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token,
          deviceInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            deviceType: 'web'
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ FCM token sent to server successfully:', result)

      return result
    } catch (err) {
      console.error('❌ Failed to send FCM token to server:', err)
      throw err
    }
  }

  /**
   * Regenerate FCM token (useful when current token is invalid)
   */
  const regenerateToken = async () => {
    try {
      console.log('🔄 Regenerating FCM token...')

      if (!messaging) {
        await initializeMessaging()
      }

      // Delete the current token first
      if (currentToken.value) {
        try {
          const { deleteToken } = await import('firebase/messaging')
          await deleteToken(messaging)
          console.log('🗑️ Old FCM token deleted')
        } catch (deleteError) {
          console.warn('⚠️ Failed to delete old token:', deleteError)
        }
      }

      // Clear current state
      currentToken.value = null
      isSubscribed.value = false

      // Get a new token
      const success = await subscribe()

      if (success) {
        console.log('✅ FCM token regenerated successfully')
        return true
      } else {
        console.error('❌ Failed to regenerate FCM token')
        return false
      }
    } catch (err) {
      console.error('❌ Token regeneration failed:', err)
      error.value = err instanceof Error ? err.message : 'Token regeneration failed'
      return false
    }
  }

  /**
   * Unsubscribe from notifications
   */
  const unsubscribe = async () => {
    try {
      if (!currentToken.value) return true

      // Delete token from backend
      await fetch(`${API_BASE_URL}/api/v1/push/unsubscribe/fcm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token: currentToken.value
        })
      })

      // Delete token from Firebase
      if (messaging) {
        const { deleteToken } = await import('firebase/messaging')
        await deleteToken(messaging)
      }

      currentToken.value = null
      isSubscribed.value = false

      // Clear localStorage backup
      localStorage.removeItem('fcm-subscribed')
      localStorage.removeItem('fcm-token')

      console.log('✅ FCM unsubscription successful')

      return true
    } catch (err) {
      console.error('❌ FCM unsubscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Unsubscription failed'
      return false
    }
  }

  /**
   * Check if already subscribed by verifying with backend
   */
  const checkSubscription = async () => {
    try {
      if (!messaging) {
        await initializeMessaging()
      }

      console.log('🔍 Checking existing FCM subscription...')

      // Quick check from localStorage first
      const localSubscribed = localStorage.getItem('fcm-subscribed') === 'true'
      const localToken = localStorage.getItem('fcm-token')

      if (localSubscribed && localToken) {
        console.log('🔍 Found subscription in localStorage, verifying...')
        currentToken.value = localToken
      }

      // Check if we have a valid FCM token
      const { getToken } = await import('firebase/messaging')

      // Register the dedicated Firebase messaging service worker
      const swRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
        scope: '/firebase-cloud-messaging-push-scope'
      })

      await navigator.serviceWorker.ready

      const token = await getToken(messaging, {
        vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY',
        serviceWorkerRegistration: swRegistration
      })

      if (token) {
        console.log('🔍 Found existing FCM token, verifying with backend...')
        currentToken.value = token

        // Verify token with backend
        const response = await fetch(`${API_BASE_URL}/api/v1/push/subscription/status`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authStore.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          isSubscribed.value = result.data?.isSubscribed || false

          console.log(`🔍 Backend subscription status: ${isSubscribed.value ? 'SUBSCRIBED' : 'NOT SUBSCRIBED'}`)

          // If we have a token but backend says not subscribed, send the token
          if (!isSubscribed.value && token) {
            console.log('🔄 Token exists but not in backend, registering...')
            await sendTokenToServer(token)
            isSubscribed.value = true
          }
        } else {
          console.warn('⚠️ Failed to check subscription status with backend')
          isSubscribed.value = false
        }
      } else {
        console.log('🔍 No FCM token found')
        isSubscribed.value = false
        currentToken.value = null
      }

      return isSubscribed.value
    } catch (err) {
      console.error('❌ Failed to check FCM subscription:', err)
      isSubscribed.value = false
      return false
    }
  }

  /**
   * Initialize the messaging system (singleton with promise lock)
   */
  const initialize = async () => {
    // Check if already initialized
    if (isInitialized.value) {
      console.log('🔥 Firebase messaging already initialized - using existing instance')
      return globalFirebaseState.messaging
    }

    // Check if initialization is already in progress
    if (globalFirebaseState.initializationPromise) {
      console.log('🔥 Firebase messaging initialization already in progress - waiting...')
      return await globalFirebaseState.initializationPromise
    }

    // Start initialization and store the promise
    console.log('🔥 Starting Firebase messaging initialization (singleton)...')

    globalFirebaseState.initializationPromise = (async () => {
      try {
        // First check browser support
        if (!('serviceWorker' in navigator)) {
          throw new Error('Service Worker not supported')
        }
        if (!('PushManager' in window)) {
          throw new Error('Push messaging not supported')
        }
        if (!('Notification' in window)) {
          throw new Error('Notifications not supported')
        }

        console.log('✅ Browser support checks passed')

        await initializeMessaging()
        isPermissionGranted.value = Notification.permission === 'granted'
        await checkSubscription()

        // Mark as initialized
        isInitialized.value = true
        console.log('✅ Firebase messaging initialization complete (singleton)')

        return globalFirebaseState.messaging
      } catch (err) {
        console.error('❌ Failed to initialize Firebase messaging:', err)
        error.value = err instanceof Error ? err.message : 'Initialization failed'
        isSupported.value = false

        // Clear the promise on error so it can be retried
        globalFirebaseState.initializationPromise = null
        throw err
      }
    })()

    return await globalFirebaseState.initializationPromise
  }

  return {
    // State
    isSupported,
    isPermissionGranted,
    isSubscribed,
    currentToken,
    error,
    isLoading,
    isInitialized,

    // Computed
    canSubscribe,

    // Compatibility properties for NotificationManager
    subscription: currentToken, // Use currentToken as subscription
    permission,

    // Methods
    initialize,
    requestPermission,
    subscribe,
    unsubscribe,
    checkSubscription,
    regenerateToken
  }
}
