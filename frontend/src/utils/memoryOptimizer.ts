// Memory Optimization Utilities
// Helps prevent memory leaks and optimize Vue reactivity

import { ref, reactive, watch, onUnmounted, type Ref } from 'vue'

interface MemoryOptimizedArray<T> {
  items: Ref<T[]>
  add: (item: T) => void
  clear: () => void
  cleanup: () => void
  getSize: () => number
}

interface MemoryOptimizedMap<K, V> {
  map: Map<K, V>
  set: (key: K, value: V) => void
  get: (key: K) => V | undefined
  delete: (key: K) => boolean
  clear: () => void
  cleanup: () => void
  getSize: () => number
}

/**
 * Create a memory-optimized reactive array with automatic cleanup
 */
export function createMemoryOptimizedArray<T>(
  maxSize: number = 100,
  cleanupInterval: number = 5 * 60 * 1000 // 5 minutes
): MemoryOptimizedArray<T> {
  const items = ref<T[]>([]) as Ref<T[]>
  let cleanupTimer: number | null = null

  const add = (item: T) => {
    items.value.push(item)
    
    // Trim array if it exceeds max size
    if (items.value.length > maxSize) {
      const removed = items.value.splice(0, items.value.length - maxSize)
      console.log(`🧹 Memory: Trimmed ${removed.length} items from array (max: ${maxSize})`)
    }
  }

  const clear = () => {
    items.value.splice(0)
    console.log('🧹 Memory: Array cleared')
  }

  const cleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
      cleanupTimer = null
    }
    clear()
  }

  const getSize = () => items.value.length

  // Set up automatic cleanup
  cleanupTimer = setInterval(() => {
    if (items.value.length > maxSize * 0.8) { // Clean when 80% full
      const toRemove = Math.floor(items.value.length * 0.2) // Remove 20%
      const removed = items.value.splice(0, toRemove)
      console.log(`🧹 Memory: Auto-cleaned ${removed.length} items from array`)
    }
  }, cleanupInterval)

  // Cleanup on component unmount
  onUnmounted(cleanup)

  return {
    items,
    add,
    clear,
    cleanup,
    getSize
  }
}

/**
 * Create a memory-optimized Map with automatic cleanup
 */
export function createMemoryOptimizedMap<K, V>(
  maxSize: number = 100,
  cleanupInterval: number = 5 * 60 * 1000 // 5 minutes
): MemoryOptimizedMap<K, V> {
  const map = new Map<K, V>()
  let cleanupTimer: number | null = null
  let accessTimes = new Map<K, number>()

  const set = (key: K, value: V) => {
    map.set(key, value)
    accessTimes.set(key, Date.now())
    
    // Trim map if it exceeds max size
    if (map.size > maxSize) {
      // Remove oldest entries
      const sortedByAccess = Array.from(accessTimes.entries())
        .sort((a, b) => a[1] - b[1])
      
      const toRemove = sortedByAccess.slice(0, map.size - maxSize)
      toRemove.forEach(([key]) => {
        map.delete(key)
        accessTimes.delete(key)
      })
      
      console.log(`🧹 Memory: Trimmed ${toRemove.length} items from map (max: ${maxSize})`)
    }
  }

  const get = (key: K): V | undefined => {
    const value = map.get(key)
    if (value !== undefined) {
      accessTimes.set(key, Date.now()) // Update access time
    }
    return value
  }

  const deleteKey = (key: K): boolean => {
    accessTimes.delete(key)
    return map.delete(key)
  }

  const clear = () => {
    map.clear()
    accessTimes.clear()
    console.log('🧹 Memory: Map cleared')
  }

  const cleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
      cleanupTimer = null
    }
    clear()
  }

  const getSize = () => map.size

  // Set up automatic cleanup of old entries
  cleanupTimer = setInterval(() => {
    const now = Date.now()
    const maxAge = 10 * 60 * 1000 // 10 minutes
    
    for (const [key, accessTime] of accessTimes.entries()) {
      if (now - accessTime > maxAge) {
        map.delete(key)
        accessTimes.delete(key)
      }
    }
  }, cleanupInterval)

  // Cleanup on component unmount
  onUnmounted(cleanup)

  return {
    map,
    set,
    get,
    delete: deleteKey,
    clear,
    cleanup,
    getSize
  }
}

/**
 * Monitor reactive object memory usage
 */
export function createMemoryMonitor(name: string) {
  let lastSize = 0
  let growthWarningThreshold = 1000 // Warn if object grows by 1000+ items

  const checkMemoryGrowth = (currentSize: number) => {
    const growth = currentSize - lastSize
    
    if (growth > growthWarningThreshold) {
      console.warn(`🚨 Memory: ${name} grew by ${growth} items (now ${currentSize})`)
    }
    
    lastSize = currentSize
  }

  return { checkMemoryGrowth }
}

/**
 * Create a memory-safe watcher that automatically cleans up
 */
export function createMemorySafeWatcher<T>(
  source: Ref<T>,
  callback: (newVal: T, oldVal: T) => void,
  options?: { immediate?: boolean }
) {
  const stopWatcher = watch(source, callback, options)
  
  // Auto-cleanup on unmount
  onUnmounted(() => {
    stopWatcher()
    console.log('🧹 Memory: Watcher cleaned up')
  })
  
  return stopWatcher
}

/**
 * Force garbage collection if available (development only)
 */
export function forceGarbageCollection() {
  if (import.meta.env.DEV && window.gc) {
    window.gc()
    console.log('🧹 Memory: Forced garbage collection')
  }
}

/**
 * Get current memory usage information
 */
export function getMemoryInfo() {
  if (!performance.memory) {
    return null
  }

  const memory = performance.memory
  return {
    used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
    total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
    percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
  }
}

/**
 * Memory optimization recommendations
 */
export function getMemoryOptimizationTips() {
  const memInfo = getMemoryInfo()
  if (!memInfo) return []

  const tips = []

  if (memInfo.percentage > 90) {
    tips.push('🚨 Memory usage is very high (>90%). Consider reducing data size or implementing pagination.')
  } else if (memInfo.percentage > 75) {
    tips.push('⚠️ Memory usage is high (>75%). Monitor for memory leaks.')
  }

  if (memInfo.used > 100) {
    tips.push('📊 Large memory usage detected. Consider lazy loading or data virtualization.')
  }

  return tips
}
