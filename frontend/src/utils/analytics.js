// Simple analytics utilities for direct use without composables
import { trackEvent, trackPageView, trackUserAction } from '@/plugins/firebase'

// Simple page tracking
export const trackPage = (pageName, pageTitle = '') => {
  trackPageView(pageName, pageTitle || pageName)
}

// Simple event tracking
export const trackClick = (element, category = 'ui') => {
  trackEvent('click', {
    element,
    category,
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

// Business-specific tracking
export const trackContactFormSubmission = (formType = 'contact') => {
  trackEvent('contact_form_submission', {
    form_type: formType,
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

export const trackServiceInterest = (serviceName) => {
  trackEvent('service_interest', {
    service_name: serviceName,
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

export const trackQuoteRequest = (serviceType, estimatedValue = 0) => {
  trackEvent('quote_request', {
    service_type: serviceType,
    estimated_value: estimatedValue,
    currency: 'EUR',
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

export const trackUserEngagement = (action, element = '') => {
  trackEvent('user_engagement', {
    engagement_type: action,
    element,
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

export const trackError = (errorType, errorMessage = '') => {
  trackEvent('error', {
    error_type: errorType,
    error_message: errorMessage,
    page: window.location.pathname,
    user_agent: navigator.userAgent,
    timestamp: new Date().toISOString()
  })
}

// Energy consultation specific
export const trackEnergyConsultationRequest = (consultationType, serviceType) => {
  trackEvent('energy_consultation_request', {
    consultation_type: consultationType, // 'b2b' or 'b2c'
    service_type: serviceType, // 'audit', 'optimization', 'renewable', etc.
    page: window.location.pathname,
    timestamp: new Date().toISOString()
  })
}

export default {
  trackPage,
  trackClick,
  trackContactFormSubmission,
  trackServiceInterest,
  trackQuoteRequest,
  trackUserEngagement,
  trackError,
  trackEnergyConsultationRequest
}
