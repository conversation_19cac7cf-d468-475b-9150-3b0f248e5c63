// Standalone Activity Tracker - No Vue dependencies
// Can be used outside of Vue component context

class ActivityTracker {
  private isActive = false
  private lastActivity = Date.now()
  private idleTimeout = 15 * 60 * 1000 // 15 minutes
  private checkInterval: number | null = null
  private eventListeners: Array<{ event: string; handler: EventListener }> = []
  private authStore: any = null
  private lastAuthUpdate = 0
  private readonly AUTH_THROTTLE_MS = 30000 // Update auth store every 30 seconds max

  // Throttling for activity handler
  private lastActivityUpdate = 0
  private readonly ACTIVITY_THROTTLE_MS = 2000 // Throttle activity updates to every 2 seconds
  private throttleTimer: number | null = null

  // Optimized events - reduced from 6 to 3 most important ones
  private readonly activityEvents = [
    'mousedown',  // Mouse clicks
    'keypress',   // Keyboard input
    'touchstart'  // Touch interactions
    // Removed: mousemove (too frequent), scroll (too frequent), click (redundant with mousedown)
  ]

  constructor(idleTimeoutMs = 15 * 60 * 1000) {
    this.idleTimeout = idleTimeoutMs
  }

  // Throttled activity handler to prevent excessive calls
  private handleActivity = () => {
    const now = Date.now()

    // Always update last activity time (this is lightweight)
    this.lastActivity = now

    // Throttle expensive operations
    if (now - this.lastActivityUpdate < this.ACTIVITY_THROTTLE_MS) {
      return // Skip if called too recently
    }

    this.lastActivityUpdate = now

    // Update activity state
    if (!this.isActive) {
      this.isActive = true
      console.log('🟢 User activity detected')
    }

    // Update auth store if available and throttled
    if (this.authStore && now - this.lastAuthUpdate > this.AUTH_THROTTLE_MS) {
      this.lastAuthUpdate = now
      try {
        if (this.authStore.isAuthenticated && !this.authStore.isLocked) {
          this.authStore.updateActivity()
        }
      } catch (error) {
        console.warn('Failed to update auth store activity:', error)
      }
    }
  }

  // Debounced activity handler for high-frequency events
  private debouncedActivity = () => {
    // Always update timestamp immediately
    this.lastActivity = Date.now()

    // Debounce the expensive operations
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer)
    }

    this.throttleTimer = window.setTimeout(() => {
      this.handleActivity()
      this.throttleTimer = null
    }, 1000) // 1 second debounce
  }

  // Check for idle state - optimized to run less frequently
  private checkIdleState = () => {
    const now = Date.now()
    const timeSinceLastActivity = now - this.lastActivity

    if (timeSinceLastActivity >= this.idleTimeout && this.isActive) {
      this.isActive = false
      console.log('🔴 User idle detected')

      // Dispatch custom event for idle state
      window.dispatchEvent(new CustomEvent('user-idle', {
        detail: { idleTime: timeSinceLastActivity }
      }))
    }
  }

  // Adaptive interval checking - checks more frequently when close to idle timeout
  private getCheckInterval = () => {
    const timeSinceLastActivity = Date.now() - this.lastActivity
    const timeUntilIdle = this.idleTimeout - timeSinceLastActivity

    // If close to idle timeout (within 2 minutes), check every 30 seconds
    if (timeUntilIdle <= 2 * 60 * 1000) {
      return 30000 // 30 seconds
    }

    // If recently active, check every 2 minutes
    if (timeUntilIdle <= 5 * 60 * 1000) {
      return 2 * 60 * 1000 // 2 minutes
    }

    // If far from idle timeout, check every 5 minutes
    return 5 * 60 * 1000 // 5 minutes
  }

  // Start tracking user activity with optimized event handling
  startTracking() {
    if (this.checkInterval) {
      console.warn('Activity tracker already running')
      return
    }

    console.log('🔍 Starting optimized activity tracking')

    // Add throttled event listeners for high-frequency events
    this.activityEvents.forEach(event => {
      // Use debounced handler for better performance
      const handler = this.debouncedActivity
      document.addEventListener(event, handler, {
        passive: true,
        capture: false // Don't capture, just bubble
      })
      this.eventListeners.push({ event, handler })
    })

    // Start adaptive interval checking
    this.scheduleNextCheck()

    // Initial activity
    this.handleActivity()
  }

  // Schedule next idle check with adaptive interval
  private scheduleNextCheck = () => {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    const interval = this.getCheckInterval()
    console.log(`🕐 Scheduling next idle check in ${interval / 1000}s`)

    this.checkInterval = window.setInterval(() => {
      this.checkIdleState()
      // Reschedule with potentially different interval
      this.scheduleNextCheck()
    }, interval)
  }

  // Stop tracking with complete cleanup
  stopTracking() {
    console.log('🛑 Stopping activity tracking')

    // Remove event listeners
    this.eventListeners.forEach(({ event, handler }) => {
      document.removeEventListener(event, handler)
    })
    this.eventListeners = []

    // Clear intervals and timers
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer)
      this.throttleTimer = null
    }

    console.log('🧹 Activity tracker cleanup completed')
  }

  // Get current activity state
  getActivityState() {
    return {
      isActive: this.isActive,
      lastActivity: this.lastActivity,
      timeSinceLastActivity: Date.now() - this.lastActivity
    }
  }

  // Check if user is currently idle
  isUserIdle() {
    return Date.now() - this.lastActivity >= this.idleTimeout
  }

  // Update idle timeout
  setIdleTimeout(timeoutMs: number) {
    this.idleTimeout = timeoutMs
    console.log(`🕐 Idle timeout updated to ${timeoutMs / 1000 / 60} minutes`)
  }

  // Set auth store for activity updates
  setAuthStore(authStore: any) {
    this.authStore = authStore
    console.log('🔐 Auth store connected to activity tracker')
  }
}

// Create global instance
const globalActivityTracker = new ActivityTracker()

// Auto-start when imported (safe for lazy loading)
if (typeof window !== 'undefined') {
  globalActivityTracker.startTracking()
  
  // Add to window for debugging
  if (import.meta.env.DEV) {
    ;(window as any).activityTracker = globalActivityTracker
  }
  
  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    globalActivityTracker.stopTracking()
  })
}

export default globalActivityTracker
export { ActivityTracker }
