/**
 * Comprehensive Performance Profiler for HLenergy Frontend
 * Tracks Core Web Vitals, component performance, and provides optimization insights
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  type: 'timing' | 'navigation' | 'resource' | 'paint' | 'layout' | 'custom'
  details?: any
}

interface WebVitalsMetric {
  name: 'FCP' | 'LCP' | 'FID' | 'CLS' | 'TTFB' | 'INP'
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
}

interface ComponentPerformance {
  name: string
  mountTime: number
  renderTime: number
  updateCount: number
  lastUpdate: number
}

class PerformanceProfiler {
  private metrics: PerformanceMetric[] = []
  private webVitals: WebVitalsMetric[] = []
  private componentMetrics = new Map<string, ComponentPerformance>()
  private observer: PerformanceObserver | null = null
  private isEnabled = false
  private startTime = performance.now()

  constructor() {
    this.isEnabled = import.meta.env.DEV || localStorage.getItem('enable-profiler') === 'true'
    if (this.isEnabled) {
      this.initialize()
    }
  }

  private initialize() {
    // Initialize Performance Observer
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry)
        }
      })

      // Observe different types of performance entries
      try {
        this.observer.observe({ entryTypes: ['navigation', 'resource', 'paint', 'layout-shift', 'largest-contentful-paint', 'first-input'] })
      } catch (e) {
        console.warn('Some performance metrics not supported:', e)
      }
    }

    // Track Core Web Vitals
    this.trackWebVitals()

    // Track initial page load metrics
    this.trackInitialMetrics()

    // Set up periodic reporting
    setInterval(() => this.generateReport(), 30000) // Every 30 seconds
  }

  private processPerformanceEntry(entry: PerformanceEntry) {
    const metric: PerformanceMetric = {
      name: entry.name,
      value: entry.duration || (entry as any).value || 0,
      timestamp: entry.startTime,
      type: this.getEntryType(entry.entryType),
      details: this.extractEntryDetails(entry)
    }

    this.metrics.push(metric)

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500)
    }
  }

  private getEntryType(entryType: string): PerformanceMetric['type'] {
    switch (entryType) {
      case 'navigation': return 'navigation'
      case 'resource': return 'resource'
      case 'paint': return 'paint'
      case 'layout-shift': return 'layout'
      default: return 'timing'
    }
  }

  private extractEntryDetails(entry: PerformanceEntry): any {
    if (entry.entryType === 'resource') {
      const resourceEntry = entry as PerformanceResourceTiming
      return {
        transferSize: resourceEntry.transferSize,
        encodedBodySize: resourceEntry.encodedBodySize,
        decodedBodySize: resourceEntry.decodedBodySize,
        initiatorType: resourceEntry.initiatorType
      }
    }
    
    if (entry.entryType === 'navigation') {
      const navEntry = entry as PerformanceNavigationTiming
      return {
        domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
        loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
        ttfb: navEntry.responseStart - navEntry.requestStart
      }
    }

    return {}
  }

  private trackWebVitals() {
    // First Contentful Paint (FCP)
    this.observeWebVital('first-contentful-paint', 'FCP', [1800, 3000])

    // Largest Contentful Paint (LCP)
    this.observeWebVital('largest-contentful-paint', 'LCP', [2500, 4000])

    // Cumulative Layout Shift (CLS)
    this.observeLayoutShift()

    // First Input Delay (FID) / Interaction to Next Paint (INP)
    this.observeInteractionMetrics()

    // Time to First Byte (TTFB)
    this.observeTTFB()
  }

  private observeWebVital(entryType: string, name: WebVitalsMetric['name'], thresholds: [number, number]) {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const value = entry.startTime || (entry as any).value || 0
            this.addWebVital(name, value, thresholds)
          }
        })
        observer.observe({ entryTypes: [entryType] })
      } catch (e) {
        console.warn(`Cannot observe ${entryType}:`, e)
      }
    }
  }

  private observeLayoutShift() {
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.addWebVital('CLS', clsValue, [0.1, 0.25])
        })
        observer.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        console.warn('Cannot observe layout-shift:', e)
      }
    }
  }

  private observeInteractionMetrics() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const value = (entry as any).processingStart - entry.startTime
            this.addWebVital('FID', value, [100, 300])
          }
        })
        observer.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        console.warn('Cannot observe first-input:', e)
      }
    }
  }

  private observeTTFB() {
    const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navEntry) {
      const ttfb = navEntry.responseStart - navEntry.requestStart
      this.addWebVital('TTFB', ttfb, [800, 1800])
    }
  }

  private addWebVital(name: WebVitalsMetric['name'], value: number, thresholds: [number, number]) {
    const rating: WebVitalsMetric['rating'] = 
      value <= thresholds[0] ? 'good' : 
      value <= thresholds[1] ? 'needs-improvement' : 'poor'

    this.webVitals.push({
      name,
      value,
      rating,
      timestamp: performance.now()
    })
  }

  private trackInitialMetrics() {
    // Track when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.addCustomMetric('DOM Ready', performance.now() - this.startTime)
      })
    } else {
      this.addCustomMetric('DOM Ready', 0) // Already loaded
    }

    // Track when page is fully loaded
    if (document.readyState !== 'complete') {
      window.addEventListener('load', () => {
        this.addCustomMetric('Page Load Complete', performance.now() - this.startTime)
      })
    }
  }

  // Public API
  public addCustomMetric(name: string, value: number, details?: any) {
    if (!this.isEnabled) return

    this.metrics.push({
      name,
      value,
      timestamp: performance.now(),
      type: 'custom',
      details
    })
  }

  public startComponentTimer(componentName: string) {
    if (!this.isEnabled) return

    const existing = this.componentMetrics.get(componentName)
    if (existing) {
      existing.updateCount++
      existing.lastUpdate = performance.now()
    } else {
      this.componentMetrics.set(componentName, {
        name: componentName,
        mountTime: performance.now(),
        renderTime: 0,
        updateCount: 1,
        lastUpdate: performance.now()
      })
    }
  }

  public endComponentTimer(componentName: string) {
    if (!this.isEnabled) return

    const component = this.componentMetrics.get(componentName)
    if (component) {
      component.renderTime = performance.now() - component.lastUpdate
    }
  }

  public getWebVitals(): WebVitalsMetric[] {
    return [...this.webVitals]
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  public getComponentMetrics(): ComponentPerformance[] {
    return Array.from(this.componentMetrics.values())
  }

  public generateReport(): PerformanceReport {
    const report: PerformanceReport = {
      timestamp: Date.now(),
      webVitals: this.getWebVitalsReport(),
      resourceMetrics: this.getResourceMetrics(),
      componentMetrics: this.getComponentMetrics(),
      recommendations: this.getRecommendations(),
      summary: this.getSummary()
    }

    if (import.meta.env.DEV) {
      console.group('🚀 Performance Report')
      console.table(report.webVitals)
      console.log('📊 Resource Metrics:', report.resourceMetrics)
      console.log('🔧 Recommendations:', report.recommendations)
      console.groupEnd()
    }

    return report
  }

  private getWebVitalsReport() {
    return this.webVitals.map(vital => ({
      metric: vital.name,
      value: Math.round(vital.value),
      rating: vital.rating,
      unit: vital.name === 'CLS' ? 'score' : 'ms'
    }))
  }

  private getResourceMetrics() {
    const resourceEntries = this.metrics.filter(m => m.type === 'resource')
    const totalSize = resourceEntries.reduce((sum, entry) => sum + (entry.details?.transferSize || 0), 0)
    const slowResources = resourceEntries.filter(entry => entry.value > 1000)

    return {
      totalResources: resourceEntries.length,
      totalSize: Math.round(totalSize / 1024), // KB
      slowResources: slowResources.length,
      averageLoadTime: resourceEntries.length > 0 ? 
        Math.round(resourceEntries.reduce((sum, entry) => sum + entry.value, 0) / resourceEntries.length) : 0
    }
  }

  private getRecommendations(): string[] {
    const recommendations: string[] = []
    const vitals = this.getWebVitalsReport()

    vitals.forEach(vital => {
      if (vital.rating === 'poor') {
        switch (vital.metric) {
          case 'FCP':
            recommendations.push('Optimize First Contentful Paint: Reduce render-blocking resources, optimize CSS delivery')
            break
          case 'LCP':
            recommendations.push('Optimize Largest Contentful Paint: Optimize images, improve server response times')
            break
          case 'FID':
            recommendations.push('Optimize First Input Delay: Reduce JavaScript execution time, use web workers')
            break
          case 'CLS':
            recommendations.push('Optimize Cumulative Layout Shift: Set image dimensions, avoid dynamic content insertion')
            break
          case 'TTFB':
            recommendations.push('Optimize Time to First Byte: Improve server performance, use CDN')
            break
        }
      }
    })

    const resourceMetrics = this.getResourceMetrics()
    if (resourceMetrics.slowResources > 5) {
      recommendations.push('Multiple slow resources detected: Consider lazy loading, code splitting, or CDN')
    }

    if (resourceMetrics.totalSize > 2000) { // 2MB
      recommendations.push('Large bundle size detected: Implement code splitting and tree shaking')
    }

    return recommendations
  }

  private getSummary() {
    const vitals = this.getWebVitalsReport()
    const goodVitals = vitals.filter(v => v.rating === 'good').length
    const totalVitals = vitals.length

    return {
      overallScore: totalVitals > 0 ? Math.round((goodVitals / totalVitals) * 100) : 0,
      criticalIssues: vitals.filter(v => v.rating === 'poor').length,
      totalMetrics: this.metrics.length,
      componentsTracked: this.componentMetrics.size
    }
  }

  public enable() {
    this.isEnabled = true
    localStorage.setItem('enable-profiler', 'true')
    if (!this.observer) {
      this.initialize()
    }
  }

  public disable() {
    this.isEnabled = false
    localStorage.removeItem('enable-profiler')
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  }

  public clear() {
    this.metrics = []
    this.webVitals = []
    this.componentMetrics.clear()
  }
}

interface PerformanceReport {
  timestamp: number
  webVitals: Array<{
    metric: string
    value: number
    rating: string
    unit: string
  }>
  resourceMetrics: {
    totalResources: number
    totalSize: number
    slowResources: number
    averageLoadTime: number
  }
  componentMetrics: ComponentPerformance[]
  recommendations: string[]
  summary: {
    overallScore: number
    criticalIssues: number
    totalMetrics: number
    componentsTracked: number
  }
}

// Create global instance
export const performanceProfiler = new PerformanceProfiler()

// Add to window for debugging
if (import.meta.env.DEV) {
  ;(window as any).performanceProfiler = performanceProfiler
}

export default performanceProfiler
