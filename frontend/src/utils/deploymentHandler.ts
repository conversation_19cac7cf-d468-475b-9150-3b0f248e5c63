/**
 * Deployment Handler
 * Handles deployment-related issues like chunk loading failures
 */

export class DeploymentHandler {
  private static instance: DeploymentHandler
  private reloadAttempts = 0
  private maxReloadAttempts = 3
  private reloadCooldown = 5000 // 5 seconds

  static getInstance(): DeploymentHandler {
    if (!DeploymentHandler.instance) {
      DeploymentHandler.instance = new DeploymentHandler()
    }
    return DeploymentHandler.instance
  }

  /**
   * Initialize deployment error handling
   */
  initialize(): void {
    this.setupChunkErrorHandling()
    this.setupNavigationErrorHandling()
    console.log('🚀 Deployment handler initialized')
  }

  /**
   * Setup chunk loading error handling
   */
  private setupChunkErrorHandling(): void {
    // Handle script loading errors
    window.addEventListener('error', (event) => {
      const target = event.target as HTMLElement
      
      if (target && target.tagName === 'SCRIPT') {
        const src = (target as HTMLScriptElement).src
        
        if (src && this.isChunkFile(src)) {
          console.warn('🔄 Chunk loading failed:', src)
          this.handleChunkError(src)
        }
      }
    })

    // Handle dynamic import errors
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason
      
      if (error && error.message && this.isChunkError(error.message)) {
        console.warn('🔄 Dynamic import failed:', error.message)
        event.preventDefault()
        this.handleChunkError(error.message)
      }
    })
  }

  /**
   * Setup navigation error handling
   */
  private setupNavigationErrorHandling(): void {
    // This will be handled by the router error handler
    console.log('📍 Navigation error handling delegated to router')
  }

  /**
   * Check if the error is related to chunk loading
   */
  private isChunkError(message: string): boolean {
    return (
      message.includes('Loading chunk') ||
      message.includes('Failed to fetch dynamically imported module') ||
      message.includes('Loading CSS chunk') ||
      message.includes('ChunkLoadError')
    )
  }

  /**
   * Check if the URL is a chunk file
   */
  private isChunkFile(url: string): boolean {
    return (
      url.includes('chunk') ||
      /\-[a-f0-9]{8,}\.(js|css)/.test(url) ||
      url.includes('assets/')
    )
  }

  /**
   * Handle chunk loading error
   */
  private handleChunkError(source: string): void {
    if (this.reloadAttempts >= this.maxReloadAttempts) {
      console.error('🚨 Max reload attempts reached, stopping automatic reloads')
      this.showManualReloadPrompt()
      return
    }

    this.reloadAttempts++
    console.log(`🔄 Attempting reload ${this.reloadAttempts}/${this.maxReloadAttempts}`)

    // Clear caches and reload after a short delay
    setTimeout(() => {
      this.clearCachesAndReload()
    }, this.reloadCooldown)
  }

  /**
   * Clear all caches and reload the page
   */
  private async clearCachesAndReload(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
        console.log('✅ All caches cleared before reload')
      }
    } catch (error) {
      console.warn('Failed to clear caches:', error)
    } finally {
      window.location.reload()
    }
  }

  /**
   * Show manual reload prompt when automatic reloads fail
   */
  private showManualReloadPrompt(): void {
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #dc2626;
      color: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.4);
      z-index: 10001;
      max-width: 400px;
      text-align: center;
      font-family: system-ui, -apple-system, sans-serif;
    `

    notification.innerHTML = `
      <div style="margin-bottom: 16px;">
        <strong>🚨 Update Required</strong>
      </div>
      <div style="margin-bottom: 20px; opacity: 0.9; line-height: 1.5;">
        A new version has been deployed. Please refresh the page to continue.
      </div>
      <button id="manual-reload-btn" style="
        background: white;
        color: #dc2626;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        font-size: 14px;
      ">Refresh Page</button>
    `

    document.body.appendChild(notification)

    // Handle reload button click
    const reloadBtn = notification.querySelector('#manual-reload-btn')
    reloadBtn?.addEventListener('click', () => {
      this.clearCachesAndReload()
    })
  }

  /**
   * Reset reload attempts (useful for testing)
   */
  resetReloadAttempts(): void {
    this.reloadAttempts = 0
  }

  /**
   * Force a cache clear and reload
   */
  forceReload(): void {
    this.clearCachesAndReload()
  }
}

// Export singleton instance
export const deploymentHandler = DeploymentHandler.getInstance()

// Auto-initialize when module is imported
if (typeof window !== 'undefined') {
  deploymentHandler.initialize()
}
