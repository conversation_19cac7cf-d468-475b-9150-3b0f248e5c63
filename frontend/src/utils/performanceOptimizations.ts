/**
 * Performance Optimization Utilities
 * Collection of utilities to improve frontend performance
 */

// Critical CSS inlining (lightweight)
export function inlineCriticalCSS() {
  // Minimal critical styles for faster initial render
  const criticalStyles = `
    body { margin: 0; font-family: system-ui, -apple-system, sans-serif; }
    .hero { min-height: 100vh; display: flex; align-items: center; }
  `

  const style = document.createElement('style')
  style.textContent = criticalStyles
  document.head.insertBefore(style, document.head.firstChild)
}

// Preload critical resources
export function preloadCriticalResources() {
  const criticalResources = [
    { href: '/hl-energy-logo-192w.png', as: 'image' },
    { href: '/pwa-192x192.png', as: 'image' },
  ]

  criticalResources.forEach(resource => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = resource.href
    link.as = resource.as
    document.head.appendChild(link)
  })
}

// Lazy load images with intersection observer
export function setupLazyImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          if (img.dataset.src) {
            img.src = img.dataset.src
            img.classList.remove('lazy')
            imageObserver.unobserve(img)
          }
        }
      })
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })

    // Observe all lazy images
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img)
    })

    return imageObserver
  }
  return null
}

// Optimize font loading
export function optimizeFontLoading() {
  // Preload critical fonts
  const criticalFonts = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  ]

  criticalFonts.forEach(fontUrl => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = fontUrl
    link.as = 'style'
    link.onload = () => {
      link.rel = 'stylesheet'
    }
    document.head.appendChild(link)
  })
}

// Debounce function for performance
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// Throttle function for performance
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Optimize scroll performance
export function optimizeScrollPerformance() {
  let ticking = false

  const updateScrollPosition = () => {
    // Update scroll-dependent elements here
    ticking = false
  }

  const onScroll = () => {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition)
      ticking = true
    }
  }

  window.addEventListener('scroll', onScroll, { passive: true })
  
  return () => window.removeEventListener('scroll', onScroll)
}

// Reduce layout thrashing
export function batchDOMUpdates(updates: (() => void)[]) {
  requestAnimationFrame(() => {
    updates.forEach(update => update())
  })
}

// Optimize animations
export function createOptimizedAnimation(
  element: HTMLElement,
  keyframes: Keyframe[],
  options: KeyframeAnimationOptions
) {
  // Use transform and opacity for better performance
  const optimizedKeyframes = keyframes.map(frame => ({
    ...frame,
    // Ensure we're using compositor-friendly properties
    transform: frame.transform || 'translateZ(0)',
  }))

  return element.animate(optimizedKeyframes, {
    ...options,
    // Enable hardware acceleration
    composite: 'replace',
  })
}

// Simple lazy loading for images
export function setupImageLazyLoading() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          if (img.dataset.src) {
            img.src = img.dataset.src
            imageObserver.unobserve(img)
          }
        }
      })
    })

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img)
    })
  }
}

// Optimize bundle loading
export function preloadRoute(routeName: string) {
  // This would work with your router's lazy loading
  const routeMap: Record<string, () => Promise<any>> = {
    'about': () => import('../views/AboutView.vue'),
    'services': () => import('../views/ServicesView.vue'),
    'contact': () => import('../views/ContactView.vue'),
    'dashboard': () => import('../views/DashboardView.vue'),
  }

  const loader = routeMap[routeName]
  if (loader) {
    // Preload on idle
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => loader())
    } else {
      setTimeout(() => loader(), 100)
    }
  }
}

// Service Worker optimization
export function optimizeServiceWorker() {
  if ('serviceWorker' in navigator) {
    // Skip waiting for faster updates
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      window.location.reload()
    })

    // Handle updates
    navigator.serviceWorker.addEventListener('message', event => {
      if (event.data && event.data.type === 'SKIP_WAITING') {
        navigator.serviceWorker.controller?.postMessage({ type: 'SKIP_WAITING' })
      }
    })
  }
}

// Critical resource hints
export function addResourceHints() {
  const hints = [
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com', crossorigin: 'anonymous' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' },
  ]

  hints.forEach(hint => {
    const link = document.createElement('link')
    link.rel = hint.rel
    link.href = hint.href
    if (hint.crossorigin) {
      link.crossOrigin = hint.crossorigin
    }
    document.head.appendChild(link)
  })
}

// Initialize lightweight optimizations
export function initializePerformanceOptimizations() {
  // Run critical optimizations immediately
  inlineCriticalCSS()
  preloadCriticalResources()
  addResourceHints()

  // Run non-critical optimizations after load
  window.addEventListener('load', () => {
    setupImageLazyLoading()
    optimizeScrollPerformance()
  })
}

export default {
  inlineCriticalCSS,
  preloadCriticalResources,
  setupLazyImages,
  optimizeFontLoading,
  debounce,
  throttle,
  optimizeScrollPerformance,
  batchDOMUpdates,
  createOptimizedAnimation,
  createVirtualList,
  preloadRoute,
  optimizeServiceWorker,
  addResourceHints,
  initializePerformanceOptimizations
}
