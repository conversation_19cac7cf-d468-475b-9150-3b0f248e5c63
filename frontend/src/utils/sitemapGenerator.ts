/**
 * Sitemap generator for HLenergy website
 * Generates XML sitemaps for better SEO
 */

interface SitemapUrl {
  loc: string
  lastmod?: string
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
  alternates?: { hreflang: string; href: string }[]
}

interface SitemapOptions {
  baseUrl: string
  defaultChangefreq?: SitemapUrl['changefreq']
  defaultPriority?: number
  includeAlternates?: boolean
  languages?: string[]
}

class SitemapGenerator {
  private options: Required<SitemapOptions>

  constructor(options: SitemapOptions) {
    this.options = {
      defaultChangefreq: 'weekly',
      defaultPriority: 0.5,
      includeAlternates: true,
      languages: ['en', 'es', 'pt'],
      ...options
    }
  }

  /**
   * Generate main sitemap XML
   */
  generateSitemap(): string {
    const urls = this.getStaticUrls()
    
    const urlElements = urls.map(url => this.generateUrlElement(url)).join('\n')
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urlElements}
</urlset>`
  }

  /**
   * Generate sitemap index for multiple sitemaps
   */
  generateSitemapIndex(): string {
    const sitemaps = [
      {
        loc: `${this.options.baseUrl}/sitemap-main.xml`,
        lastmod: new Date().toISOString().split('T')[0]
      },
      {
        loc: `${this.options.baseUrl}/sitemap-services.xml`,
        lastmod: new Date().toISOString().split('T')[0]
      }
    ]

    const sitemapElements = sitemaps.map(sitemap => 
      `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`
    ).join('\n')

    return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapElements}
</sitemapindex>`
  }

  /**
   * Get static URLs for the website
   */
  private getStaticUrls(): SitemapUrl[] {
    const baseUrls: Omit<SitemapUrl, 'alternates'>[] = [
      {
        loc: '/',
        changefreq: 'daily',
        priority: 1.0,
        lastmod: new Date().toISOString().split('T')[0]
      },
      {
        loc: '/about',
        changefreq: 'monthly',
        priority: 0.8,
        lastmod: '2024-06-01'
      },
      {
        loc: '/services',
        changefreq: 'weekly',
        priority: 0.9,
        lastmod: '2024-06-01'
      },
      {
        loc: '/services/energy-audit',
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: '2024-06-01'
      },
      {
        loc: '/services/renewable-energy',
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: '2024-06-01'
      },
      {
        loc: '/services/energy-efficiency',
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: '2024-06-01'
      },
      {
        loc: '/services/sustainability-consulting',
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: '2024-06-01'
      },
      {
        loc: '/contact',
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: '2024-06-01'
      },
      {
        loc: '/login',
        changefreq: 'yearly',
        priority: 0.3,
        lastmod: '2024-06-01'
      },
      {
        loc: '/register',
        changefreq: 'yearly',
        priority: 0.3,
        lastmod: '2024-06-01'
      }
    ]

    // Add alternates for each URL if enabled
    return baseUrls.map(url => ({
      ...url,
      loc: `${this.options.baseUrl}${url.loc}`,
      alternates: this.options.includeAlternates ? this.generateAlternates(url.loc) : undefined
    }))
  }

  /**
   * Generate alternate language URLs
   */
  private generateAlternates(path: string): { hreflang: string; href: string }[] {
    return this.options.languages.map(lang => ({
      hreflang: lang,
      href: `${this.options.baseUrl}/${lang}${path}`
    }))
  }

  /**
   * Generate URL element XML
   */
  private generateUrlElement(url: SitemapUrl): string {
    let element = `  <url>
    <loc>${url.loc}</loc>`

    if (url.lastmod) {
      element += `\n    <lastmod>${url.lastmod}</lastmod>`
    }

    if (url.changefreq) {
      element += `\n    <changefreq>${url.changefreq}</changefreq>`
    }

    if (url.priority !== undefined) {
      element += `\n    <priority>${url.priority}</priority>`
    }

    // Add alternate language links
    if (url.alternates && url.alternates.length > 0) {
      const alternates = url.alternates.map(alt => 
        `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
      ).join('\n')
      element += `\n${alternates}`
    }

    element += '\n  </url>'
    return element
  }

  /**
   * Generate robots.txt content
   */
  generateRobotsTxt(): string {
    return `User-agent: *
Allow: /

# Disallow admin and private areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /profile/
Disallow: /api/

# Disallow temporary and cache files
Disallow: /*.json$
Disallow: /temp/
Disallow: /cache/

# Allow important files
Allow: /assets/
Allow: /images/
Allow: /*.css$
Allow: /*.js$

# Sitemap location
Sitemap: ${this.options.baseUrl}/sitemap.xml
Sitemap: ${this.options.baseUrl}/sitemap-index.xml

# Crawl delay (be respectful)
Crawl-delay: 1

# Specific rules for major search engines
User-agent: Googlebot
Crawl-delay: 0

User-agent: Bingbot
Crawl-delay: 1

User-agent: Slurp
Crawl-delay: 2`
  }

  /**
   * Generate structured data for organization
   */
  generateOrganizationStructuredData(): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'HLenergy',
      alternateName: 'HL Energy Consulting',
      description: 'Professional energy consultation services to optimize your energy consumption and reduce costs',
      url: this.options.baseUrl,
      logo: `${this.options.baseUrl}/hl-energy-logo-256w.png`,
      image: `${this.options.baseUrl}/hl-energy-logo-256w.png`,
      foundingDate: '2008',
      numberOfEmployees: '50+',
      industry: 'Energy Consulting',
      serviceArea: {
        '@type': 'Place',
        name: 'Worldwide'
      },
      contactPoint: [
        {
          '@type': 'ContactPoint',
          telephone: '+351912345678',
          contactType: 'customer service',
          availableLanguage: ['English', 'Spanish', 'Portuguese'],
          areaServed: 'Worldwide'
        },
        {
          '@type': 'ContactPoint',
          email: '<EMAIL>',
          contactType: 'customer service',
          availableLanguage: ['English', 'Spanish', 'Portuguese']
        }
      ],
      address: {
        '@type': 'PostalAddress',
        addressLocality: 'Portugal',
        addressCountry: 'PT'
      },
      sameAs: [
        'https://www.linkedin.com/company/hlenergy',
        'https://twitter.com/hlenergy',
        'https://www.facebook.com/hlenergy'
      ],
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Energy Consulting Services',
        itemListElement: [
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Energy Audit',
              description: 'Comprehensive energy assessment and optimization recommendations'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Renewable Energy Consulting',
              description: 'Solar, wind, and other renewable energy solutions'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Energy Efficiency Optimization',
              description: 'Reduce energy consumption and costs through efficiency improvements'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Sustainability Consulting',
              description: 'Environmental impact assessment and sustainability strategies'
            }
          }
        ]
      }
    }
  }
}

// Create default instance
export const sitemapGenerator = new SitemapGenerator({
  baseUrl: 'https://hlenergy.com' // This should be configurable based on environment
})

export default SitemapGenerator
