import './assets/main.css'

// Suppress workbox console logs early
import './utils/suppressWorkboxLogs'

// Initialize deployment handler for chunk loading errors
import './utils/deploymentHandler'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'

import App from './App.vue'
import router from './router'
import i18n from './i18n'
import { useAuthStore } from './stores/auth'

// Handle chunk load errors (when old JS files are missing after deployment)
window.addEventListener('error', (event) => {
  const target = event.target as HTMLElement

  // Check if it's a script loading error
  if (target && target.tagName === 'SCRIPT') {
    const src = (target as HTMLScriptElement).src

    // Check if it's a chunk loading error (contains hash in filename)
    if (src && (src.includes('chunk') || /\-[a-f0-9]{8,}\.(js|css)/.test(src))) {
      console.warn('🔄 Chunk loading failed, reloading page:', src)

      // Clear caches and reload
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            caches.delete(cacheName)
          })
        }).finally(() => {
          window.location.reload()
        })
      } else {
        window.location.reload()
      }
    }
  }
})

// Handle dynamic import errors
window.addEventListener('unhandledrejection', (event) => {
  const error = event.reason

  // Check if it's a chunk loading error
  if (error && error.message && (
    error.message.includes('Loading chunk') ||
    error.message.includes('Failed to fetch dynamically imported module') ||
    error.message.includes('Loading CSS chunk')
  )) {
    console.warn('🔄 Dynamic import failed, reloading page:', error.message)
    event.preventDefault() // Prevent the error from being logged

    // Clear caches and reload
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          caches.delete(cacheName)
        })
      }).finally(() => {
        window.location.reload()
      })
    } else {
      window.location.reload()
    }
  }
})

// Socket.io will be lazy loaded when needed

// Import analytics
// import { analytics } from './services/analytics'

// Firebase Analytics will be lazy loaded when needed

const app = createApp(App)
const pinia = createPinia()
const head = createHead()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(head)
// Socket.io plugin will be loaded lazily

// Initialize auth state and mount app
const initializeApp = async () => {
  const authStore = useAuthStore()

  // Initialize auth state before mounting
  try {
    await authStore.initializeAuth()
  } catch (error) {
    console.warn('Auth initialization failed:', error)
  }

  // Lazy load Socket.io after initial render using requestIdleCallback for better performance
  const loadSocketIO = () => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(async () => {
        try {
          const { default: socketPlugin } = await import('./plugins/socket')
          app.use(socketPlugin)
          console.log('🔌 Socket.io loaded lazily')
        } catch (error) {
          console.warn('Failed to load Socket.io:', error)
        }
      }, { timeout: 2000 })
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(async () => {
        try {
          const { default: socketPlugin } = await import('./plugins/socket')
          app.use(socketPlugin)
          console.log('🔌 Socket.io loaded lazily')
        } catch (error) {
          console.warn('Failed to load Socket.io:', error)
        }
      }, 1000)
    }
  }

  loadSocketIO()

  app.mount('#app')
}

// Start the app
initializeApp()

// Development utilities removed for cleaner production builds
// PWA test utilities and other development tools have been removed
// to improve performance and reduce bundle size
