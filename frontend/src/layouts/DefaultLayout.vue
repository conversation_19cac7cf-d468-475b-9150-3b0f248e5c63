<template>
  <div class="min-h-screen flex flex-col bg-base-200">
    <AppHeader v-if="!hideHeader" />

    <main class="flex-1 bg-base-200">
      <slot />
    </main>

    <AppFooter v-if="!hideFooter" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

const route = useRoute()

// Check if current route should hide header/footer
const hideHeader = computed(() => route.meta.hideHeader === true)
const hideFooter = computed(() => route.meta.hideFooter === true)
</script>
