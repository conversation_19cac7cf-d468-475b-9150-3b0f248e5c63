<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Lead Management</h1>
        <p class="text-base-content/70 mt-1">Manage and track all your leads</p>
      </div>
      <div class="flex gap-2">
        <button @click="exportLeads" class="btn btn-outline btn-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </button>
        <button @click="refreshLeads" class="btn btn-primary btn-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Search</span>
            </label>
            <input 
              v-model="filters.search" 
              type="text" 
              placeholder="Search by name or email..." 
              class="input input-bordered input-sm"
              @input="debouncedSearch"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">Status</span>
            </label>
            <select v-model="filters.status" @change="fetchLeads" class="select select-bordered select-sm">
              <option value="">All Statuses</option>
              <option value="new">New</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">Sort By</span>
            </label>
            <select v-model="filters.sortBy" @change="fetchLeads" class="select select-bordered select-sm">
              <option value="created_at">Date Created</option>
              <option value="updated_at">Date Updated</option>
              <option value="name">Name</option>
              <option value="email">Email</option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">Order</span>
            </label>
            <select v-model="filters.sortOrder" @change="fetchLeads" class="select select-bordered select-sm">
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="stat bg-base-100 shadow-lg rounded-lg">
        <div class="stat-title">Total Leads</div>
        <div class="stat-value text-primary">{{ pagination.total }}</div>
        <div class="stat-desc">All time</div>
      </div>
      <div class="stat bg-base-100 shadow-lg rounded-lg">
        <div class="stat-title">New Leads</div>
        <div class="stat-value text-info">{{ getStatusCount('new') }}</div>
        <div class="stat-desc">Awaiting contact</div>
      </div>
      <div class="stat bg-base-100 shadow-lg rounded-lg">
        <div class="stat-title">In Progress</div>
        <div class="stat-value text-warning">{{ getStatusCount('in_progress') }}</div>
        <div class="stat-desc">Being processed</div>
      </div>
      <div class="stat bg-base-100 shadow-lg rounded-lg">
        <div class="stat-title">Resolved</div>
        <div class="stat-value text-success">{{ getStatusCount('resolved') }}</div>
        <div class="stat-desc">Completed</div>
      </div>
    </div>

    <!-- Leads Table -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Lead</th>
                <th>Contact Info</th>
                <th>Message</th>
                <th>Status</th>
                <th>Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading">
                <td colspan="6" class="text-center py-8">
                  <span class="loading loading-spinner loading-lg"></span>
                </td>
              </tr>
              <tr v-else-if="leads.length === 0">
                <td colspan="6" class="text-center text-base-content/60 py-8">
                  No leads found matching your criteria.
                </td>
              </tr>
              <tr v-else v-for="lead in leads" :key="lead.id" 
                  @click="openLeadModal(lead.id)" 
                  class="hover:bg-base-200 cursor-pointer">
                <td>
                  <div class="flex items-center space-x-3">
                    <div class="avatar placeholder">
                      <div class="bg-primary text-primary-content rounded-full w-10 h-10">
                        <span class="text-sm">{{ getInitials(lead.name) }}</span>
                      </div>
                    </div>
                    <div>
                      <div class="font-bold">{{ lead.name }}</div>
                      <div class="text-sm opacity-50">ID: {{ lead.id }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <div class="font-medium">{{ lead.email }}</div>
                    <div class="text-sm opacity-50">{{ lead.phone }}</div>
                  </div>
                </td>
                <td>
                  <div class="max-w-xs truncate">
                    {{ lead.message || 'No message' }}
                  </div>
                </td>
                <td>
                  <div class="badge" :class="{
                    'badge-primary': lead.status === 'new',
                    'badge-warning': lead.status === 'in_progress',
                    'badge-success': lead.status === 'resolved'
                  }">
                    {{ getStatusLabel(lead.status) }}
                  </div>
                </td>
                <td>
                  <div>
                    <div class="text-sm">{{ formatDate(lead.created_at) }}</div>
                    <div class="text-xs opacity-50">{{ formatTimeAgo(lead.created_at) }}</div>
                  </div>
                </td>
                <td @click.stop>
                  <div class="flex gap-1">
                    <button @click="openLeadModal(lead.id)" class="btn btn-ghost btn-xs">View</button>
                    <button @click="sendEmail(lead)" class="btn btn-ghost btn-xs">Email</button>
                    <button @click="makeCall(lead)" class="btn btn-ghost btn-xs">Call</button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.pages > 1" class="flex justify-center mt-6">
          <div class="btn-group">
            <button 
              @click="goToPage(pagination.page - 1)" 
              :disabled="pagination.page <= 1"
              class="btn btn-sm"
            >
              «
            </button>
            <button 
              v-for="page in getVisiblePages()" 
              :key="page"
              @click="goToPage(page)"
              :class="['btn btn-sm', { 'btn-active': page === pagination.page }]"
            >
              {{ page }}
            </button>
            <button 
              @click="goToPage(pagination.page + 1)" 
              :disabled="pagination.page >= pagination.pages"
              class="btn btn-sm"
            >
              »
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Lead Details Modal -->
  <LeadDetailsModal 
    :is-open="isLeadModalOpen" 
    :lead-id="selectedLeadId"
    @close="closeLeadModal"
    @updated="onLeadUpdated"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { contactService } from '@/services/contact'
import LeadDetailsModal from '@/components/admin/LeadDetailsModal.vue'

interface Lead {
  id: number
  name: string
  email: string
  phone: string
  message: string
  status: string
  created_at: string
  updated_at: string
}

interface Pagination {
  page: number
  limit: number
  total: number
  pages: number
}

// Reactive data
const leads = ref<Lead[]>([])
const loading = ref(false)
const pagination = ref<Pagination>({
  page: 1,
  limit: 20,
  total: 0,
  pages: 0
})

const filters = ref({
  search: '',
  status: '',
  sortBy: 'created_at',
  sortOrder: 'desc'
})

// Modal state
const isLeadModalOpen = ref(false)
const selectedLeadId = ref<number | null>(null)

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    pagination.value.page = 1 // Reset to first page on search
    fetchLeads()
  }, 500)
}

// Fetch leads
const fetchLeads = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      sortBy: filters.value.sortBy,
      sortOrder: filters.value.sortOrder,
      ...(filters.value.status && { status: filters.value.status }),
      ...(filters.value.search && { search: filters.value.search })
    }

    const response = await contactService.getContactSubmissions(params)
    
    leads.value = response.items || []
    pagination.value = response.pagination || pagination.value
  } catch (error) {
    console.error('Failed to fetch leads:', error)
    leads.value = []
  } finally {
    loading.value = false
  }
}

// Computed properties
const getStatusCount = (status: string) => {
  return leads.value.filter(lead => lead.status === status).length
}

// Helper functions
const getInitials = (name: string) => {
  if (!name) return 'UN'
  const nameParts = name.trim().split(' ')
  const firstName = nameParts[0] || ''
  const lastName = nameParts[1] || ''
  return `${firstName.charAt(0) || ''}${lastName.charAt(0) || ''}`.toUpperCase()
}

const getStatusLabel = (status: string) => {
  const statusMap = {
    'new': 'New',
    'in_progress': 'In Progress',
    'resolved': 'Resolved'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'Unknown'
  return new Date(dateString).toLocaleDateString()
}

const formatTimeAgo = (dateString: string) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  return date.toLocaleDateString()
}

// Pagination
const goToPage = (page: number) => {
  if (page >= 1 && page <= pagination.value.pages) {
    pagination.value.page = page
    fetchLeads()
  }
}

const getVisiblePages = () => {
  const current = pagination.value.page
  const total = pagination.value.pages
  const delta = 2
  
  const range = []
  const rangeWithDots = []
  
  for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
    range.push(i)
  }
  
  if (current - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }
  
  rangeWithDots.push(...range)
  
  if (current + delta < total - 1) {
    rangeWithDots.push('...', total)
  } else if (total > 1) {
    rangeWithDots.push(total)
  }
  
  return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index)
}

// Modal functions
const openLeadModal = (leadId: number) => {
  selectedLeadId.value = leadId
  isLeadModalOpen.value = true
}

const closeLeadModal = () => {
  isLeadModalOpen.value = false
  selectedLeadId.value = null
}

const onLeadUpdated = () => {
  fetchLeads()
}

// Actions
const refreshLeads = () => {
  fetchLeads()
}

const exportLeads = async () => {
  try {
    const blob = await contactService.exportContactSubmissions(filters.value)
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `leads-export-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Export failed:', error)
  }
}

const sendEmail = (lead: Lead) => {
  window.open(`mailto:${lead.email}?subject=Re: Your inquiry&body=Hello ${lead.name},`)
}

const makeCall = (lead: Lead) => {
  window.open(`tel:${lead.phone}`)
}

// Initialize
onMounted(() => {
  fetchLeads()
})
</script>
