<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- <PERSON>er -->
    <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300">
      <div class="max-w-7xl mx-auto px-6 py-8">
        <div class="text-center">
          <div class="flex justify-center mb-4">
            <div class="p-4 bg-primary/20 rounded-full">
              <Icon name="cog" size="2xl" class="text-primary" />
            </div>
          </div>
          <h1 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
            Admin Dashboard
          </h1>
          <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
            System administration and management center
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      
      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
        <div class="stat bg-primary text-primary-content rounded-lg shadow-lg">
          <div class="stat-title text-primary-content/70">Total Users</div>
          <div class="stat-value">{{ totalUsers.toLocaleString() }}</div>
          <div class="stat-desc text-primary-content/70">{{ activeUsers }} active</div>
        </div>
        <div class="stat bg-secondary text-secondary-content rounded-lg shadow-lg">
          <div class="stat-title text-secondary-content/70">System Health</div>
          <div class="stat-value">{{ systemHealth }}%</div>
          <div class="stat-desc text-secondary-content/70">All systems operational</div>
        </div>
        <div class="stat bg-accent text-accent-content rounded-lg shadow-lg">
          <div class="stat-title text-accent-content/70">API Requests</div>
          <div class="stat-value">{{ apiRequests.toLocaleString() }}</div>
          <div class="stat-desc text-accent-content/70">Last 24 hours</div>
        </div>
        <div class="stat bg-info text-info-content rounded-lg shadow-lg">
          <div class="stat-title text-info-content/70">Storage Used</div>
          <div class="stat-value">{{ storageUsed }}%</div>
          <div class="stat-desc text-info-content/70">{{ storageTotal }} total</div>
        </div>
      </div>

      <!-- Admin Navigation Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- User Management -->
        <RouterLink 
          to="/admin/users"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-primary/20 rounded-xl mr-4">
                <Icon name="users" size="lg" class="text-primary" />
              </div>
              <div>
                <h3 class="text-xl font-bold">User Management</h3>
                <p class="text-base-content/60">Manage users and permissions</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              {{ totalUsers }} total users
            </div>
          </div>
        </RouterLink>

        <!-- System Settings -->
        <RouterLink 
          to="/admin/system"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-secondary/20 rounded-xl mr-4">
                <Icon name="cog" size="lg" class="text-secondary" />
              </div>
              <div>
                <h3 class="text-xl font-bold">System Settings</h3>
                <p class="text-base-content/60">Configure system parameters</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              Health: {{ systemHealth }}%
            </div>
          </div>
        </RouterLink>

        <!-- Database Management -->
        <RouterLink 
          to="/admin/database"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-accent/20 rounded-xl mr-4">
                <Icon name="database" size="lg" class="text-accent" />
              </div>
              <div>
                <h3 class="text-xl font-bold">Database</h3>
                <p class="text-base-content/60">Database operations and backup</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              Storage: {{ storageUsed }}% used
            </div>
          </div>
        </RouterLink>

        <!-- Security Settings -->
        <RouterLink 
          to="/admin/security"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-warning/20 rounded-xl mr-4">
                <Icon name="shield" size="lg" class="text-warning" />
              </div>
              <div>
                <h3 class="text-xl font-bold">Security</h3>
                <p class="text-base-content/60">Security policies and monitoring</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              All systems secure
            </div>
          </div>
        </RouterLink>

        <!-- System Logs -->
        <RouterLink 
          to="/admin/logs"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-info/20 rounded-xl mr-4">
                <Icon name="document" size="lg" class="text-info" />
              </div>
              <div>
                <h3 class="text-xl font-bold">System Logs</h3>
                <p class="text-base-content/60">View and analyze logs</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              Recent activity
            </div>
          </div>
        </RouterLink>

        <!-- Performance Monitor -->
        <RouterLink 
          to="/admin/performance"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
        >
          <div class="card-body">
            <div class="flex items-center mb-4">
              <div class="p-3 bg-success/20 rounded-xl mr-4">
                <Icon name="chart-bar" size="lg" class="text-success" />
              </div>
              <div>
                <h3 class="text-xl font-bold">Performance</h3>
                <p class="text-base-content/60">Monitor system performance</p>
              </div>
            </div>
            <div class="text-sm text-base-content/50">
              Real-time metrics
            </div>
          </div>
        </RouterLink>

      </div>

      <!-- Quick Actions -->
      <div class="mt-8">
        <h2 class="text-2xl font-bold mb-6">Quick Actions</h2>
        <div class="flex flex-wrap gap-4">
          <RouterLink to="/crm" class="btn btn-primary">
            <Icon name="chart-bar" class="mr-2" />
            CRM Dashboard
          </RouterLink>
          <RouterLink to="/dashboard" class="btn btn-secondary">
            <Icon name="home" class="mr-2" />
            Main Dashboard
          </RouterLink>
          <RouterLink to="/admin/leads" class="btn btn-accent">
            <Icon name="users" class="mr-2" />
            Lead Management
          </RouterLink>
          <button @click="refreshSystem" class="btn btn-info">
            <Icon name="refresh" class="mr-2" />
            Refresh System
          </button>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

const authStore = useAuthStore()

// Mock data (replace with real API calls)
const totalUsers = ref(1247)
const activeUsers = ref(89)
const systemHealth = ref(98)
const apiRequests = ref(15420)
const storageUsed = ref(67)
const storageTotal = ref('2.4TB')

// Quick actions
const refreshSystem = () => {
  // Implement system refresh
  console.log('Refreshing system data...')
}
</script>

<style scoped>
.stat {
  @apply p-6;
}

.card {
  @apply cursor-pointer;
}

.card:hover {
  @apply shadow-2xl;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .grid {
    @apply grid-cols-1;
  }
}
</style>
