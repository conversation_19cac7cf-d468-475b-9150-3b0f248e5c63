<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- <PERSON>er -->
    <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300">
      <div class="max-w-7xl mx-auto px-6 py-8">
        <div class="text-center">
          <div class="flex justify-center mb-4">
            <div class="p-4 bg-primary/20 rounded-full">
              <Icon name="cog" size="2xl" class="text-primary" />
            </div>
          </div>
          <h1 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
            Admin Dashboard
          </h1>
          <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
            Complete system administration and management center
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      
      <!-- Tab Navigation -->
      <div class="tabs tabs-boxed bg-base-200 p-1 mb-8 overflow-x-auto">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab tab-lg whitespace-nowrap', { 'tab-active': activeTab === tab.id }]"
        >
          <Icon :name="tab.icon" class="mr-2" />
          {{ tab.name }}
        </button>
      </div>

      <!-- Tab Content with Lazy Loading -->
      <div class="tab-content">
        
        <!-- Dashboard Tab -->
        <div v-if="activeTab === 'dashboard'" class="space-y-8">
          <Suspense>
            <AdminDashboard />
            <template #fallback>
              <LoadingSpinner message="Loading dashboard..." />
            </template>
          </Suspense>
          
          <!-- Quick Stats -->
          <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <div class="stat bg-primary text-primary-content rounded-lg">
              <div class="stat-title text-primary-content/70">Total Users</div>
              <div class="stat-value">{{ totalUsers.toLocaleString() }}</div>
              <div class="stat-desc text-primary-content/70">{{ activeUsers }} active</div>
            </div>
            <div class="stat bg-secondary text-secondary-content rounded-lg">
              <div class="stat-title text-secondary-content/70">System Health</div>
              <div class="stat-value">{{ systemHealth }}%</div>
              <div class="stat-desc text-secondary-content/70">All systems operational</div>
            </div>
            <div class="stat bg-accent text-accent-content rounded-lg">
              <div class="stat-title text-accent-content/70">API Requests</div>
              <div class="stat-value">{{ apiRequests.toLocaleString() }}</div>
              <div class="stat-desc text-accent-content/70">Last 24 hours</div>
            </div>
            <div class="stat bg-info text-info-content rounded-lg">
              <div class="stat-title text-info-content/70">Storage Used</div>
              <div class="stat-value">{{ storageUsed }}%</div>
              <div class="stat-desc text-info-content/70">{{ storageTotal }} total</div>
            </div>
          </div>
        </div>

        <!-- Users Tab -->
        <div v-else-if="activeTab === 'users'" class="space-y-6">
          <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold">User Management</h2>
            <button @click="showUserManagement = true" class="btn btn-primary">
              <Icon name="plus" class="mr-2" />
              Add User
            </button>
          </div>
          <Suspense>
            <UserManagement />
            <template #fallback>
              <LoadingSpinner message="Loading user management..." />
            </template>
          </Suspense>
        </div>

        <!-- System Tab -->
        <div v-else-if="activeTab === 'system'" class="space-y-6">
          <h2 class="text-2xl font-bold">System Settings</h2>
          <Suspense>
            <SystemSettings />
            <template #fallback>
              <LoadingSpinner message="Loading system settings..." />
            </template>
          </Suspense>
        </div>

        <!-- Database Tab -->
        <div v-else-if="activeTab === 'database'" class="space-y-6">
          <h2 class="text-2xl font-bold">Database Management</h2>
          <Suspense>
            <DatabaseManagement />
            <template #fallback>
              <LoadingSpinner message="Loading database management..." />
            </template>
          </Suspense>
        </div>

        <!-- Security Tab -->
        <div v-else-if="activeTab === 'security'" class="space-y-6">
          <h2 class="text-2xl font-bold">Security Settings</h2>
          <Suspense>
            <SecuritySettings />
            <template #fallback>
              <LoadingSpinner message="Loading security settings..." />
            </template>
          </Suspense>
        </div>

        <!-- Logs Tab -->
        <div v-else-if="activeTab === 'logs'" class="space-y-6">
          <h2 class="text-2xl font-bold">System Logs</h2>
          <Suspense>
            <SystemLogs />
            <template #fallback>
              <LoadingSpinner message="Loading system logs..." />
            </template>
          </Suspense>
        </div>

        <!-- Monitoring Tab -->
        <div v-else-if="activeTab === 'monitoring'" class="space-y-6">
          <h2 class="text-2xl font-bold">System Monitoring</h2>
          <Suspense>
            <SocketMonitor />
            <template #fallback>
              <LoadingSpinner message="Loading monitoring tools..." />
            </template>
          </Suspense>
        </div>

      </div>
    </div>

    <!-- Modals (only render when needed) -->
    <UserManagement 
      v-if="showUserManagement" 
      @close="showUserManagement = false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'

// Lazy load admin components for better performance
const AdminDashboard = defineAsyncComponent(() => import('@/components/admin/AdminDashboard.vue'))
const UserManagement = defineAsyncComponent(() => import('@/components/admin/UserManagement.vue'))
const SystemSettings = defineAsyncComponent(() => import('@/components/admin/SystemSettings.vue'))
const DatabaseManagement = defineAsyncComponent(() => import('@/components/admin/DatabaseManagement.vue'))
const SecuritySettings = defineAsyncComponent(() => import('@/components/admin/SecuritySettings.vue'))
const SystemLogs = defineAsyncComponent(() => import('@/components/admin/SystemLogs.vue'))
const SocketMonitor = defineAsyncComponent(() => import('@/components/admin/SocketMonitor.vue'))

const authStore = useAuthStore()

// Tab management for lazy loading
const activeTab = ref('dashboard')

// Tab configuration
const tabs = [
  { id: 'dashboard', name: 'Dashboard', icon: 'home' },
  { id: 'users', name: 'Users', icon: 'users' },
  { id: 'system', name: 'System', icon: 'cog' },
  { id: 'database', name: 'Database', icon: 'database' },
  { id: 'security', name: 'Security', icon: 'shield' },
  { id: 'logs', name: 'Logs', icon: 'document' },
  { id: 'monitoring', name: 'Monitoring', icon: 'wifi' }
]

// Modal states
const showUserManagement = ref(false)

// Mock data (replace with real API calls)
const totalUsers = ref(1247)
const activeUsers = ref(89)
const systemHealth = ref(98)
const apiRequests = ref(15420)
const storageUsed = ref(67)
const storageTotal = ref('2.4TB')
</script>

<style scoped>
.tab-content {
  min-height: 400px;
}

.stat {
  @apply p-6 shadow-lg;
}

/* Smooth transitions */
.tab {
  @apply transition-all duration-200;
}

.tab-active {
  @apply transform scale-105;
}

/* Mobile responsive tabs */
@media (max-width: 768px) {
  .tabs {
    @apply flex-nowrap;
  }
  
  .tab {
    @apply flex-shrink-0;
  }
}
</style>
