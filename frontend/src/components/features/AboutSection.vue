<template>
  <section class="py-12 bg-gradient-to-br from-base-200/50 to-base-100">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <div class="flex justify-center mb-6">
          <Logo size="lg" :show-text="false" />
        </div>
        <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ t('about_section.title') }}</h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto mb-8 leading-relaxed">{{ t('about_section.subtitle') }}</p>
        <div class="w-32 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"></div>
      </div>

      <!-- Company Introduction -->
      <div class="mb-12">
        <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50">
          <div class="card-body p-8 lg:p-12">
            <p class="text-lg text-base-content/80 leading-relaxed text-center max-w-4xl mx-auto">
              {{ t('about_section.intro') }}
            </p>
          </div>
        </div>
      </div>

      <!-- B2B and B2C Solutions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-12">
        <!-- B2B Solutions -->
        <div class="group">
          <div class="card bg-gradient-to-br from-primary/10 to-primary/15 border border-primary/25 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 bg-white/70 dark:bg-base-100/70 backdrop-blur-sm">
            <div class="card-body p-8">
              <div class="flex items-center mb-6">
                <div class="p-4 bg-gradient-to-br from-primary/35 to-primary/15 rounded-2xl mr-4 shadow-lg border border-primary/25 group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                  <Icon name="building" size="xl" class="text-primary drop-shadow-sm" />
                </div>
                <h3 class="text-2xl font-bold text-primary">{{ t('about_section.b2b_title') }}</h3>
              </div>
              <p class="text-base-content/80 leading-relaxed text-lg">
                {{ t('about_section.b2b_description') }}
              </p>
              <div class="mt-6">
                <RouterLink to="/services" class="btn btn-primary btn-outline">
                  <Icon name="arrow-right" size="sm" class="mr-2" />
                  {{ t('about_section.business_services') }}
                </RouterLink>
              </div>
            </div>
          </div>
        </div>

        <!-- B2C Solutions -->
        <div class="group">
          <div class="card bg-gradient-to-br from-secondary/20 to-secondary/10 border border-secondary/35 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 bg-white/70 dark:bg-base-100/70 backdrop-blur-sm">
            <div class="card-body p-8">
              <div class="flex items-center mb-6">
                <div class="p-4 bg-gradient-to-br from-secondary/35 to-secondary/15 rounded-2xl mr-4 shadow-lg border border-secondary/25 group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                  <Icon name="home" size="xl" class="text-secondary drop-shadow-sm" />
                </div>
                <h3 class="text-2xl font-bold text-secondary">{{ t('about_section.b2c_title') }}</h3>
              </div>
              <p class="text-base-content/80 leading-relaxed text-lg">
                {{ t('about_section.b2c_description') }}
              </p>
              <div class="mt-6">
                <RouterLink to="/contact" class="btn btn-secondary btn-outline">
                  <Icon name="home" size="sm" class="mr-2" />
                  {{ t('about_section.residential_services') }}
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Why Choose HLenergy -->
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold mb-12 text-base-content">{{ t('about_section.why_choose_title') }}</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8">
          <!-- Expertise -->
          <div class="text-center group">
            <div class="p-6 bg-accent/10 rounded-2xl mb-4 group-hover:bg-accent/20 transition-colors duration-300">
              <Icon name="academic" size="2xl" class="text-accent mx-auto mb-4" />
              <h4 class="font-semibold text-base-content mb-2">Experience</h4>
              <p class="text-sm text-base-content/70">{{ t('about_section.expertise') }}</p>
            </div>
          </div>

          <!-- Comprehensive -->
          <div class="text-center group">
            <div class="p-6 bg-info/10 rounded-2xl mb-4 group-hover:bg-info/20 transition-colors duration-300">
              <Icon name="globe" size="2xl" class="text-info mx-auto mb-4" />
              <h4 class="font-semibold text-base-content mb-2">Comprehensive</h4>
              <p class="text-sm text-base-content/70">{{ t('about_section.comprehensive') }}</p>
            </div>
          </div>

          <!-- Certified -->
          <div class="text-center group">
            <div class="p-6 bg-success/10 rounded-2xl mb-4 group-hover:bg-success/20 transition-colors duration-300">
              <Icon name="shield" size="2xl" class="text-success mx-auto mb-4" />
              <h4 class="font-semibold text-base-content mb-2">Certified</h4>
              <p class="text-sm text-base-content/70">{{ t('about_section.certified') }}</p>
            </div>
          </div>

          <!-- Results -->
          <div class="text-center group">
            <div class="p-6 bg-warning/10 rounded-2xl mb-4 group-hover:bg-warning/20 transition-colors duration-300">
              <Icon name="chart-bar" size="2xl" class="text-warning mx-auto mb-4" />
              <h4 class="font-semibold text-base-content mb-2">Results</h4>
              <p class="text-sm text-base-content/70">{{ t('about_section.results') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'
import Logo from '@/components/common/Logo.vue'

const { t } = useI18n()
</script>

<style scoped>
/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-4px) scale(1.02);
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity, color;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
