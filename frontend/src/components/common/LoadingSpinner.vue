<template>
  <div class="loading-spinner-container">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p v-if="message" class="loading-message">{{ message }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  message?: string
  size?: 'sm' | 'md' | 'lg'
}

withDefaults(defineProps<Props>(), {
  message: 'Loading...',
  size: 'md'
})
</script>

<style scoped>
.loading-spinner-container {
  @apply flex items-center justify-center p-8;
  min-height: 200px;
}

.loading-spinner {
  @apply text-center;
}

.spinner {
  @apply inline-block w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.loading-message {
  @apply text-gray-600 text-sm;
}

/* Size variations */
.loading-spinner-container.sm {
  min-height: 100px;
  @apply p-4;
}

.loading-spinner-container.sm .spinner {
  @apply w-6 h-6 border-2 mb-2;
}

.loading-spinner-container.lg {
  min-height: 300px;
  @apply p-12;
}

.loading-spinner-container.lg .spinner {
  @apply w-12 h-12 border-4 mb-6;
}
</style>
