<template>
  <div class="logo-container" :class="containerClasses">
    <picture>
      <!-- WebP format for modern browsers -->
      <source 
        :srcset="webpSrcset" 
        type="image/webp"
      />
      <!-- AVIF format for even better compression -->
      <source 
        :srcset="avifSrcset" 
        type="image/avif"
      />
      <!-- Fallback PNG -->
      <img 
        :src="fallbackSrc"
        :alt="alt"
        :class="imageClasses"
        :width="width"
        :height="height"
        loading="lazy"
        decoding="async"
      />
    </picture>
    
    <!-- Optional text logo for accessibility -->
    <span v-if="showText" :class="textClasses">
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  variant?: 'default' | 'white' | 'dark'
  showText?: boolean
  text?: string
  alt?: string
  class?: string
  responsive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default',
  showText: false,
  text: 'HL Energy',
  alt: 'HL Energy - Professional Energy Consultation',
  class: '',
  responsive: true
})

// Size configurations
const sizeConfig = {
  xs: { width: 32, height: 32, textSize: 'text-sm' },
  sm: { width: 48, height: 48, textSize: 'text-base' },
  md: { width: 64, height: 64, textSize: 'text-lg' },
  lg: { width: 96, height: 96, textSize: 'text-xl' },
  xl: { width: 128, height: 128, textSize: 'text-2xl' },
  '2xl': { width: 192, height: 192, textSize: 'text-3xl' }
}

const currentSize = computed(() => sizeConfig[props.size])

// Responsive width for srcset
const width = computed(() => currentSize.value.width)
const height = computed(() => currentSize.value.height)

// Generate srcset for different formats
const webpSrcset = computed(() => {
  const sizes = [32, 64, 96, 128, 192, 256]
  return sizes
    .filter(size => size >= currentSize.value.width)
    .map(size => `/src/assets/optimized/hl-energy-logo-${size}w.webp ${size}w`)
    .join(', ')
})

const avifSrcset = computed(() => {
  const sizes = [32, 64, 96, 128, 192, 256]
  return sizes
    .filter(size => size >= currentSize.value.width)
    .map(size => `/src/assets/optimized/hl-energy-logo-${size}w.avif ${size}w`)
    .join(', ')
})

const fallbackSrc = computed(() => {
  // Use the closest optimized size
  const targetWidth = currentSize.value.width
  const availableSizes = [32, 64, 96, 128, 192, 256]
  const closestSize = availableSizes.find(size => size >= targetWidth) || 256
  return `/src/assets/optimized/hl-energy-logo-${closestSize}w.png`
})

// CSS classes
const containerClasses = computed(() => [
  'flex items-center gap-2',
  props.class
])

const imageClasses = computed(() => [
  'logo-image',
  'transition-all duration-300',
  {
    'hover:scale-105': props.responsive,
    'filter brightness-0 invert': props.variant === 'white',
    'filter brightness-0': props.variant === 'dark'
  }
])

const textClasses = computed(() => [
  'font-bold font-heading text-base-content',
  currentSize.value.textSize,
  {
    'text-white': props.variant === 'white',
    'text-base-content': props.variant === 'default',
    'text-gray-900': props.variant === 'dark'
  }
])
</script>

<style scoped>
.logo-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
  margin: auto;
}

picture {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Ensure logo maintains aspect ratio and is vertically centered */
.logo-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Smooth transitions for theme changes */
.logo-image {
  transition: filter 0.3s ease-in-out, transform 0.2s ease-in-out;
}

/* High DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
</style>
