<template>
  <div class="recaptcha-v3">
    <!-- reCAPTCHA v3 is invisible, but we show a status indicator -->
    <div
      class="card border shadow-sm p-4 transition-all duration-300"
      :class="{
        'bg-base-100 border-base-200/50': !hasError && !isVerified,
        'bg-error/5 border-error/30 animate-pulse': hasError,
        'bg-success/5 border-success/30': isVerified,
        'bg-warning/5 border-warning/30': isLoading
      }"
    >
      <div class="flex items-center gap-3">
        <!-- Status Icon -->
        <div class="flex-shrink-0">
          <div v-if="isLoading" class="loading loading-spinner loading-sm text-primary"></div>
          <Icon v-else-if="isVerified" name="check-circle" size="sm" class="text-success" />
          <Icon v-else-if="hasError" name="x-circle" size="sm" class="text-error" />
          <Icon v-else name="shield" size="sm" class="text-base-content/60" />
        </div>

        <!-- Status Text -->
        <div class="flex-1">
          <div class="flex items-center gap-2">
            <span
              class="text-sm font-medium"
              :class="{
                'text-error': hasError,
                'text-success': isVerified,
                'text-warning': isLoading,
                'text-base-content': !hasError && !isVerified && !isLoading
              }"
            >
              {{ statusText }}
            </span>
            
            <!-- Google reCAPTCHA Badge -->
            <div class="text-xs text-base-content/50">
              Protected by reCAPTCHA
            </div>
          </div>
          
          <!-- Error message -->
          <div v-if="hasError && errorMessage" class="text-xs text-error mt-1">
            {{ errorMessage }}
          </div>
        </div>

        <!-- Retry Button -->
        <div v-if="hasError" class="flex-shrink-0">
          <button
            @click="executeRecaptcha"
            class="btn btn-sm btn-outline btn-error"
            :disabled="isLoading"
          >
            <Icon name="refresh" size="sm" class="mr-1" />
            {{ $t('contact.captcha.retry', 'Retry') }}
          </button>
        </div>

        <!-- Debug Button (Development Only) -->
        <div v-if="isDev && (isLoading || hasError)" class="flex-shrink-0">
          <button
            @click="forceReinitialize"
            class="btn btn-xs btn-warning"
            type="button"
          >
            🔄 Force Reinit
          </button>
        </div>
      </div>
    </div>

    <!-- reCAPTCHA Terms -->
    <div class="text-xs text-base-content/50 mt-2 text-center">
      {{ $t('contact.captcha.terms_prefix', 'This site is protected by reCAPTCHA and the Google') }}
      <a href="https://policies.google.com/privacy" target="_blank" class="link link-primary">
        {{ $t('contact.captcha.privacy_policy', 'Privacy Policy') }}
      </a>
      {{ $t('contact.captcha.and', 'and') }}
      <a href="https://policies.google.com/terms" target="_blank" class="link link-primary">
        {{ $t('contact.captcha.terms_service', 'Terms of Service') }}
      </a>
      {{ $t('contact.captcha.apply', 'apply') }}.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()

// Props
interface Props {
  modelValue?: boolean
  action?: string // reCAPTCHA action name (e.g., 'contact_form')
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  action: 'contact_form'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'verified': [token: string]
  'error': [error: string]
}>()

// State
const isLoading = ref(false)
const isVerified = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const recaptchaToken = ref('')

// Timeout management
let mountTimeout: ReturnType<typeof setTimeout> | null = null

// Computed
const statusText = computed(() => {
  if (isLoading.value) return t('contact.captcha.verifying', 'Verifying...')
  if (isVerified.value) return t('contact.captcha.verified', 'Verified')
  if (hasError.value) return t('contact.captcha.error', 'Verification failed')
  return t('contact.captcha.ready', 'Ready for verification')
})

const isDev = computed(() => import.meta.env.DEV)

// reCAPTCHA configuration
const RECAPTCHA_SITE_KEY = import.meta.env.VITE_RECAPTCHA_SITE_KEY
const RECAPTCHA_SCRIPT_ID = 'recaptcha-v3-script'

// Global script reference counter
if (!window.__recaptchaInstances) {
  window.__recaptchaInstances = 0
}

// Methods
const addRecaptchaPositioningCSS = (): void => {
  // Check if CSS is already added
  if (document.getElementById('recaptcha-positioning-css')) {
    return
  }

  const style = document.createElement('style')
  style.id = 'recaptcha-positioning-css'
  style.textContent = `
    /* Adjust WhatsApp button position when reCAPTCHA badge is present */
    .grecaptcha-badge {
      z-index: 40 !important; /* Ensure reCAPTCHA badge is below WhatsApp button */
    }

    /* Move WhatsApp button when reCAPTCHA badge appears */
    body:has(.grecaptcha-badge) .fixed.bottom-6.right-6 {
      right: 6rem !important;
      transition: right 0.3s ease !important;
    }

    /* Fallback for browsers without :has() support */
    @supports not selector(:has(.grecaptcha-badge)) {
      .contact-page .fixed.bottom-6.right-6 {
        right: 6rem !important;
        transition: right 0.3s ease !important;
      }
    }

    /* Mobile adjustments */
    @media (max-width: 640px) {
      body:has(.grecaptcha-badge) .fixed.bottom-6.right-6,
      .contact-page .fixed.bottom-6.right-6 {
        right: 4rem !important;
        bottom: 1rem !important;
      }
    }
  `

  document.head.appendChild(style)
  console.log('🎨 Added reCAPTCHA positioning CSS')
}

const loadRecaptchaScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if grecaptcha is already available and functional
    if (window.grecaptcha && typeof window.grecaptcha.execute === 'function') {
      console.log('🔐 reCAPTCHA: grecaptcha already available')
      resolve()
      return
    }

    // Check if script is already loaded but grecaptcha might not be ready
    const existingScript = document.getElementById(RECAPTCHA_SCRIPT_ID)
    if (existingScript) {
      console.log('🔐 reCAPTCHA: Script exists, waiting for grecaptcha to be ready')
      // Script exists but grecaptcha might not be ready, wait for it
      const checkReady = () => {
        if (window.grecaptcha && typeof window.grecaptcha.execute === 'function') {
          console.log('🔐 reCAPTCHA: grecaptcha is now ready')
          resolve()
        } else {
          setTimeout(checkReady, 100)
        }
      }
      checkReady()
      return
    }

    console.log('🔐 reCAPTCHA: Loading script for the first time')

    const script = document.createElement('script')
    script.id = RECAPTCHA_SCRIPT_ID
    script.src = `https://www.google.com/recaptcha/api.js?render=${RECAPTCHA_SITE_KEY}`
    script.async = true
    script.defer = true

    script.onload = () => {
      console.log('🔐 reCAPTCHA: Script loaded, waiting for grecaptcha.ready')
      // Wait for grecaptcha to be ready
      if (window.grecaptcha && window.grecaptcha.ready) {
        window.grecaptcha.ready(() => {
          console.log('🔐 reCAPTCHA: grecaptcha.ready callback executed')
          // Add CSS to adjust WhatsApp button position when reCAPTCHA badge appears
          addRecaptchaPositioningCSS()
          resolve()
        })
      } else {
        // Fallback: wait a bit and check again
        console.log('🔐 reCAPTCHA: grecaptcha.ready not available, using fallback')
        setTimeout(() => {
          if (window.grecaptcha && typeof window.grecaptcha.execute === 'function') {
            console.log('🔐 reCAPTCHA: Fallback successful')
            addRecaptchaPositioningCSS()
            resolve()
          } else {
            console.error('🔐 reCAPTCHA: Fallback failed')
            reject(new Error('reCAPTCHA failed to initialize after script load'))
          }
        }, 500)
      }
    }

    script.onerror = () => {
      reject(new Error('Failed to load reCAPTCHA script'))
    }

    document.head.appendChild(script)
  })
}

const executeRecaptcha = async (): Promise<void> => {
  if (!RECAPTCHA_SITE_KEY) {
    console.warn('reCAPTCHA site key not configured')
    handleError('reCAPTCHA not configured')
    return
  }

  try {
    isLoading.value = true
    hasError.value = false
    errorMessage.value = ''

    console.log('🔐 reCAPTCHA: Starting execution...')

    // Check if we need to force a complete reload
    const hasScript = !!document.getElementById(RECAPTCHA_SCRIPT_ID)
    const hasGrecaptcha = !!(window.grecaptcha && window.grecaptcha.execute)
    const hasBadge = !!document.querySelector('.grecaptcha-badge')

    console.log('🔐 reCAPTCHA: Pre-execution state:', { hasScript, hasGrecaptcha, hasBadge })

    // If script exists but no grecaptcha or if this looks like a return visit without badge
    if (hasScript && (!hasGrecaptcha || (!hasBadge && window.__recaptchaInstances === 1))) {
      console.log('🔐 reCAPTCHA: Detected stale state, forcing complete reload')

      // Force complete cleanup
      const existingScript = document.getElementById(RECAPTCHA_SCRIPT_ID)
      if (existingScript) {
        existingScript.remove()
        console.log('🔐 reCAPTCHA: Removed stale script')
      }

      // Remove any stale grecaptcha
      if (window.grecaptcha) {
        try {
          delete (window as any).grecaptcha
          console.log('🔐 reCAPTCHA: Removed stale grecaptcha object')
        } catch (e) {
          console.warn('🔐 reCAPTCHA: Could not remove grecaptcha:', e)
        }
      }

      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // Load reCAPTCHA script if not already loaded
    await loadRecaptchaScript()

    // Additional check to ensure grecaptcha is available and ready
    if (!window.grecaptcha || typeof window.grecaptcha.execute !== 'function') {
      console.warn('🔐 reCAPTCHA: grecaptcha still not available after reload')
      throw new Error('reCAPTCHA failed to initialize properly')
    }

    // Ensure positioning CSS is present before executing
    if (!document.getElementById('recaptcha-positioning-css')) {
      addRecaptchaPositioningCSS()
    }

    // Execute reCAPTCHA with additional safety check
    console.log('🔐 reCAPTCHA: Executing with site key:', RECAPTCHA_SITE_KEY.substring(0, 20) + '...')
    const token = await window.grecaptcha.execute(RECAPTCHA_SITE_KEY, {
      action: props.action
    })

    if (token) {
      recaptchaToken.value = token
      isVerified.value = true
      emit('update:modelValue', true)
      emit('verified', token)
      console.log('🔐 reCAPTCHA: Verification successful, token received:', token.substring(0, 20) + '...')

      // Immediately check for badge and wait if needed
      let badgeCheckAttempts = 0
      const checkForBadge = () => {
        const badge = document.querySelector('.grecaptcha-badge')
        badgeCheckAttempts++

        if (badge) {
          console.log('🔐 reCAPTCHA: Badge confirmed present in DOM after', badgeCheckAttempts, 'attempts')
          // Make sure badge is visible
          if (badge instanceof HTMLElement) {
            badge.style.display = ''
            badge.style.visibility = 'visible'
            badge.style.opacity = '1'
          }
        } else if (badgeCheckAttempts < 10) {
          console.warn(`🔐 reCAPTCHA: Badge not found, attempt ${badgeCheckAttempts}/10, retrying...`)
          setTimeout(checkForBadge, 200)
        } else {
          console.error('🔐 reCAPTCHA: Badge never appeared after 10 attempts')
          // Try to force badge creation by executing again
          console.log('🔐 reCAPTCHA: Attempting to force badge creation...')
          setTimeout(() => {
            if (window.grecaptcha && window.grecaptcha.execute) {
              window.grecaptcha.execute(RECAPTCHA_SITE_KEY, { action: props.action })
                .then(() => console.log('🔐 reCAPTCHA: Force execution completed'))
                .catch(e => console.error('🔐 reCAPTCHA: Force execution failed:', e))
            }
          }, 500)
        }
      }

      // Start checking immediately
      setTimeout(checkForBadge, 100)
    } else {
      throw new Error('No token received from reCAPTCHA')
    }
  } catch (error: any) {
    console.error('reCAPTCHA execution failed:', error)

    // During development (HMR), be more lenient with errors
    if (import.meta.env.DEV && error.message.includes('Cannot read properties of undefined')) {
      console.warn('reCAPTCHA error during development (likely HMR), skipping verification')
      // In development, we can be more lenient and just mark as verified
      isVerified.value = true
      emit('update:modelValue', true)
      emit('verified', 'dev-bypass-token')
      return
    }

    handleError(error.message || 'reCAPTCHA verification failed')
  } finally {
    isLoading.value = false
  }
}

const handleError = (message: string): void => {
  hasError.value = true
  errorMessage.value = message
  isVerified.value = false
  recaptchaToken.value = ''
  emit('update:modelValue', false)
  emit('error', message)
}

const reset = (): void => {
  isLoading.value = false
  isVerified.value = false
  hasError.value = false
  errorMessage.value = ''
  recaptchaToken.value = ''
  emit('update:modelValue', false)
}

const showCaptchaWarning = (): void => {
  if (!isVerified.value) {
    hasError.value = true
    errorMessage.value = t('contact.captcha.warning_message')
  }
}

// Manual cleanup method for emergency use
const forceCleanup = (): void => {
  console.log('🧹 reCAPTCHA: Force cleanup initiated')

  // Remove reCAPTCHA badge
  const badge = document.querySelector('.grecaptcha-badge')
  if (badge) {
    badge.remove()
    console.log('🧹 reCAPTCHA: Force removed badge')
  }

  // Remove all reCAPTCHA related elements
  const elements = document.querySelectorAll('[id*="grecaptcha"], [class*="grecaptcha"], iframe[src*="recaptcha"]')
  elements.forEach(el => el.remove())

  // Reset instance counter
  window.__recaptchaInstances = 0

  console.log('🧹 reCAPTCHA: Force cleanup completed')
}

// Force reinitialize method for when badge doesn't appear
const forceReinitialize = async (): Promise<void> => {
  console.log('🔄 reCAPTCHA: Force reinitialize initiated')

  // First, cleanup everything
  forceCleanup()

  // Remove script
  const script = document.getElementById(RECAPTCHA_SCRIPT_ID)
  if (script) {
    script.remove()
    console.log('🔄 reCAPTCHA: Removed script for reinitialize')
  }

  // Remove grecaptcha object
  if (window.grecaptcha) {
    try {
      delete (window as any).grecaptcha
      console.log('🔄 reCAPTCHA: Removed grecaptcha object')
    } catch (e) {
      console.warn('🔄 reCAPTCHA: Could not remove grecaptcha:', e)
    }
  }

  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Reset state
  isLoading.value = false
  isVerified.value = false
  hasError.value = false

  // Try to execute again
  console.log('🔄 reCAPTCHA: Attempting fresh execution')
  await executeRecaptcha()
}

// Expose methods for parent component
defineExpose({
  reset,
  showCaptchaWarning,
  executeRecaptcha,
  forceCleanup,
  forceReinitialize
})

// Auto-execute reCAPTCHA when component mounts
onMounted(async () => {
  // Increment instance counter
  window.__recaptchaInstances++
  console.log(`🔐 reCAPTCHA instance mounted (${window.__recaptchaInstances} active)`)

  // Debug: Check current state
  console.log('🔐 reCAPTCHA: Current state on mount:', {
    hasGrecaptcha: !!window.grecaptcha,
    hasExecute: !!(window.grecaptcha && window.grecaptcha.execute),
    hasScript: !!document.getElementById(RECAPTCHA_SCRIPT_ID),
    hasBadge: !!document.querySelector('.grecaptcha-badge'),
    hasPositioningCSS: !!document.getElementById('recaptcha-positioning-css')
  })

  // Ensure positioning CSS is added when component mounts
  addRecaptchaPositioningCSS()

  // Use nextTick and requestIdleCallback for better performance
  await nextTick()

  // Use requestIdleCallback if available, fallback to setTimeout
  if ('requestIdleCallback' in window) {
    (window as any).requestIdleCallback(() => {
      executeRecaptcha()
    }, { timeout: 1000 })
  } else {
    // Fallback for browsers without requestIdleCallback
    mountTimeout = setTimeout(() => {
      mountTimeout = null
      executeRecaptcha()
    }, 500)
  }
})

// Cleanup
onUnmounted(() => {
  // Clear any pending mount timeout
  if (mountTimeout !== null) {
    clearTimeout(mountTimeout)
    mountTimeout = null
    console.log('🧹 reCAPTCHA: Cleared mount timeout on unmount')
  }

  // Decrement instance counter
  window.__recaptchaInstances--
  console.log(`🔐 reCAPTCHA instance unmounted (${window.__recaptchaInstances} remaining)`)

  // Only remove badge immediately if this is the last instance
  if (window.__recaptchaInstances <= 0) {
    const badge = document.querySelector('.grecaptcha-badge')
    if (badge) {
      badge.remove()
      console.log('🧹 reCAPTCHA: Removed badge (last instance unmounted)')
    }
  }

  // Remove script and all reCAPTCHA elements if no instances remain
  if (window.__recaptchaInstances <= 0) {
    // Always clean up the badge and positioning CSS (needed for page navigation)
    const recaptchaBadge = document.querySelector('.grecaptcha-badge')
    if (recaptchaBadge) {
      recaptchaBadge.remove()
      console.log('🧹 reCAPTCHA: Removed reCAPTCHA badge from DOM')
    }

    const positioningCSS = document.getElementById('recaptcha-positioning-css')
    if (positioningCSS) {
      positioningCSS.remove()
      console.log('🧹 reCAPTCHA: Removed positioning CSS')
    }

    // In development mode (HMR), be less aggressive with script cleanup to avoid issues
    if (import.meta.env.DEV) {
      console.log('🧹 reCAPTCHA: Development mode - keeping script and grecaptcha for HMR compatibility')
      window.__recaptchaInstances = 0
      return
    }

    // Production cleanup (more aggressive)
    // Remove the main script
    const script = document.getElementById(RECAPTCHA_SCRIPT_ID)
    if (script) {
      script.remove()
      console.log('🧹 reCAPTCHA: Removed script from DOM')
    }

    // Remove any other reCAPTCHA elements
    const recaptchaElements = document.querySelectorAll('[id^="___grecaptcha_cfg"]')
    recaptchaElements.forEach(element => {
      element.remove()
    })

    // Remove reCAPTCHA iframe containers
    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]')
    recaptchaIframes.forEach(iframe => {
      iframe.remove()
    })

    // Badge and positioning CSS already removed above

    // Clean up global grecaptcha object
    if (window.grecaptcha) {
      try {
        // @ts-ignore - We know this exists and want to delete it
        delete window.grecaptcha
        console.log('🧹 reCAPTCHA: Cleaned up global grecaptcha object')
      } catch (e) {
        console.warn('🧹 reCAPTCHA: Could not delete grecaptcha object:', e)
      }
    }

    // Clean up any reCAPTCHA related global variables
    Object.keys(window).forEach(key => {
      if (key.includes('grecaptcha') || key.includes('recaptcha')) {
        try {
          // @ts-ignore - Dynamic property deletion
          delete (window as any)[key]
        } catch (e) {
          // Some properties might not be deletable
        }
      }
    })

    window.__recaptchaInstances = 0
    console.log('🧹 reCAPTCHA: Complete cleanup finished')
  }
})

// Global type declaration for grecaptcha
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void
      execute: (siteKey: string, options: { action: string }) => Promise<string>
    }
    __recaptchaInstances: number
  }
}
</script>

<style scoped>
/* Additional styles for reCAPTCHA component */
.recaptcha-v3 {
  /* Ensure proper spacing and layout */
}
</style>
