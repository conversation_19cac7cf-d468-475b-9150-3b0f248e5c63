<template>
  <Teleport to="body">
    <!-- Consent Banner -->
    <Transition
      enter-active-class="transition-all duration-500 ease-out"
      enter-from-class="translate-y-full opacity-0"
      enter-to-class="translate-y-0 opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="translate-y-0 opacity-100"
      leave-to-class="translate-y-full opacity-0"
    >
      <div
        v-if="consentStore.showBanner"
        class="fixed bottom-0 left-0 right-0 z-[9999] bg-base-100/95 backdrop-blur-md border-t border-base-300/50 shadow-2xl"
      >
        <div class="max-w-7xl mx-auto p-4 sm:p-6">
          <!-- Simple Banner (default view) -->
          <div v-if="!showDetails" class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <!-- Content -->
            <div class="flex-1">
              <div class="flex items-start gap-3">
                <div class="p-2 bg-primary/20 rounded-lg flex-shrink-0">
                  <Icon name="shield-check" size="md" class="text-primary" />
                </div>
                <div>
                  <h3 class="font-semibold text-base-content mb-1">
                    We value your privacy
                  </h3>
                  <p class="text-sm text-base-content/70 leading-relaxed">
                    We use cookies and similar technologies to improve your experience, analyze site usage, and assist with marketing efforts. 
                    <button 
                      @click="showDetails = true"
                      class="text-primary hover:text-primary-focus underline font-medium"
                    >
                      Customize your preferences
                    </button>
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <button
                @click="acceptNecessaryOnly"
                class="btn btn-ghost btn-sm order-2 sm:order-1"
              >
                Necessary Only
              </button>
              <button
                @click="acceptAll"
                class="btn btn-primary btn-sm order-1 sm:order-2"
              >
                Accept All
              </button>
            </div>
          </div>

          <!-- Detailed Settings -->
          <div v-else class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-base-content">Privacy Preferences</h3>
                <p class="text-sm text-base-content/70 mt-1">
                  Choose which cookies and tracking technologies you're comfortable with
                </p>
              </div>
              <button
                @click="showDetails = false"
                class="btn btn-ghost btn-sm btn-circle"
              >
                <Icon name="x" size="sm" />
              </button>
            </div>

            <!-- Cookie Categories -->
            <div class="grid gap-4">
              <!-- Necessary Cookies -->
              <div class="p-4 bg-base-200/50 rounded-lg border border-base-300/30">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <Icon name="shield" size="sm" class="text-success" />
                    <h4 class="font-medium text-base-content">Necessary</h4>
                    <span class="badge badge-success badge-sm">Always Active</span>
                  </div>
                  <input
                    type="checkbox"
                    :checked="true"
                    disabled
                    class="checkbox checkbox-success checkbox-sm"
                  />
                </div>
                <p class="text-sm text-base-content/70">
                  Essential for the website to function properly. These cannot be disabled.
                </p>
              </div>

              <!-- Analytics Cookies -->
              <div class="p-4 bg-base-200/50 rounded-lg border border-base-300/30">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <Icon name="chart-bar" size="sm" class="text-info" />
                    <h4 class="font-medium text-base-content">Analytics</h4>
                  </div>
                  <input
                    type="checkbox"
                    v-model="localPreferences.analytics"
                    class="checkbox checkbox-primary checkbox-sm"
                  />
                </div>
                <p class="text-sm text-base-content/70">
                  Help us understand how visitors interact with our website through Firebase Analytics.
                </p>
              </div>

              <!-- Marketing Cookies -->
              <div class="p-4 bg-base-200/50 rounded-lg border border-base-300/30">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <Icon name="broadcast" size="sm" class="text-warning" />
                    <h4 class="font-medium text-base-content">Marketing</h4>
                  </div>
                  <input
                    type="checkbox"
                    v-model="localPreferences.marketing"
                    class="checkbox checkbox-primary checkbox-sm"
                  />
                </div>
                <p class="text-sm text-base-content/70">
                  Used to track visitors across websites for marketing and advertising purposes.
                </p>
              </div>

              <!-- Functional Cookies -->
              <div class="p-4 bg-base-200/50 rounded-lg border border-base-300/30">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <Icon name="cog" size="sm" class="text-secondary" />
                    <h4 class="font-medium text-base-content">Functional</h4>
                  </div>
                  <input
                    type="checkbox"
                    v-model="localPreferences.functional"
                    class="checkbox checkbox-primary checkbox-sm"
                  />
                </div>
                <p class="text-sm text-base-content/70">
                  Enable enhanced functionality like chat widgets and personalized content.
                </p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-base-300/30">
              <button
                @click="acceptNecessaryOnly"
                class="btn btn-ghost btn-sm flex-1 sm:flex-none"
              >
                Necessary Only
              </button>
              <button
                @click="saveCustomPreferences"
                class="btn btn-primary btn-sm flex-1 sm:flex-none"
              >
                Save Preferences
              </button>
              <button
                @click="acceptAll"
                class="btn btn-success btn-sm flex-1 sm:flex-none"
              >
                Accept All
              </button>
            </div>

            <!-- Privacy Policy Link -->
            <div class="text-center pt-2">
              <p class="text-xs text-base-content/60">
                For more information, read our 
                <router-link to="/privacy-policy" class="text-primary hover:text-primary-focus underline">
                  Privacy Policy
                </router-link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useConsentStore } from '@/stores/consent'
import Icon from '@/components/common/Icon.vue'

// Store
const consentStore = useConsentStore()

// State
const showDetails = ref(false)
const localPreferences = reactive({
  analytics: false,
  marketing: false,
  functional: false
})

// Methods
const acceptAll = async () => {
  await consentStore.acceptAll()
  showDetails.value = false
}

const acceptNecessaryOnly = async () => {
  await consentStore.acceptNecessaryOnly()
  showDetails.value = false
}

const saveCustomPreferences = async () => {
  await consentStore.updatePreferences(localPreferences)
  consentStore.hasConsented = true
  consentStore.consentDate = new Date().toISOString()
  consentStore.showBanner = false
  consentStore.saveConsent()

  // Emit event for other parts of the app
  window.dispatchEvent(new CustomEvent('consent-updated', {
    detail: { preferences: consentStore.preferences }
  }))

  showDetails.value = false
}

// Initialize local preferences with current store values
onMounted(() => {
  localPreferences.analytics = consentStore.preferences.analytics
  localPreferences.marketing = consentStore.preferences.marketing
  localPreferences.functional = consentStore.preferences.functional
})
</script>

<style scoped>
/* Additional styles for better mobile experience */
@media (max-width: 640px) {
  .btn {
    @apply text-xs px-3 py-2;
  }
}
</style>
