<template>
  <div class="card bg-base-100 shadow-xl border border-base-200">
    <div class="card-body">
      <div class="flex items-center gap-3 mb-6">
        <div class="p-2 bg-primary/20 rounded-lg">
          <Icon name="shield-check" size="md" class="text-primary" />
        </div>
        <div>
          <h3 class="text-xl font-semibold text-base-content">Privacy & Consent</h3>
          <p class="text-sm text-base-content/70">Manage your data and privacy preferences</p>
        </div>
      </div>

      <!-- Current Status -->
      <div class="mb-6 p-4 bg-base-200/50 rounded-lg">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium text-base-content">Consent Status</span>
          <div class="badge" :class="consentStore.hasConsented ? 'badge-success' : 'badge-warning'">
            {{ consentStore.hasConsented ? 'Consented' : 'Not Consented' }}
          </div>
        </div>
        <p class="text-sm text-base-content/70">
          {{ consentStore.hasConsented 
            ? `Consent given on ${formatDate(consentStore.consentDate)}` 
            : 'You have not provided consent for data processing' 
          }}
        </p>
      </div>

      <!-- Cookie Preferences -->
      <div class="space-y-4 mb-6">
        <h4 class="font-medium text-base-content">Cookie Preferences</h4>
        
        <!-- Necessary Cookies -->
        <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <Icon name="shield" size="sm" class="text-success" />
              <span class="font-medium text-base-content">Necessary</span>
              <span class="badge badge-success badge-sm">Always Active</span>
            </div>
            <p class="text-sm text-base-content/70">
              Essential for the website to function properly
            </p>
          </div>
          <input
            type="checkbox"
            :checked="true"
            disabled
            class="checkbox checkbox-success checkbox-sm"
          />
        </div>

        <!-- Analytics Cookies -->
        <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <Icon name="chart-bar" size="sm" class="text-info" />
              <span class="font-medium text-base-content">Analytics</span>
            </div>
            <p class="text-sm text-base-content/70">
              Help us understand how visitors interact with our website
            </p>
          </div>
          <input
            type="checkbox"
            v-model="localPreferences.analytics"
            @change="updatePreference('analytics', $event.target.checked)"
            class="checkbox checkbox-primary checkbox-sm"
          />
        </div>

        <!-- Marketing Cookies -->
        <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <Icon name="broadcast" size="sm" class="text-warning" />
              <span class="font-medium text-base-content">Marketing</span>
            </div>
            <p class="text-sm text-base-content/70">
              Used for advertising and marketing purposes
            </p>
          </div>
          <input
            type="checkbox"
            v-model="localPreferences.marketing"
            @change="updatePreference('marketing', $event.target.checked)"
            class="checkbox checkbox-primary checkbox-sm"
          />
        </div>

        <!-- Functional Cookies -->
        <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <Icon name="cog" size="sm" class="text-secondary" />
              <span class="font-medium text-base-content">Functional</span>
            </div>
            <p class="text-sm text-base-content/70">
              Enable enhanced functionality and personalization
            </p>
          </div>
          <input
            type="checkbox"
            v-model="localPreferences.functional"
            @change="updatePreference('functional', $event.target.checked)"
            class="checkbox checkbox-primary checkbox-sm"
          />
        </div>
      </div>

      <!-- Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <button
          @click="savePreferences"
          class="btn btn-primary btn-sm flex-1"
          :disabled="!hasChanges"
        >
          <Icon name="check" size="sm" class="mr-2" />
          Save Changes
        </button>

        <button
          @click="showConsentBanner"
          class="btn btn-outline btn-sm flex-1"
        >
          <Icon name="settings" size="sm" class="mr-2" />
          Show Consent Banner
        </button>

        <button
          @click="revokeAllConsent"
          class="btn btn-error btn-outline btn-sm flex-1"
        >
          <Icon name="error" size="sm" class="mr-2" />
          Revoke All Consent
        </button>
      </div>

      <!-- Information -->
      <div class="mt-6 p-3 bg-info/10 border border-info/20 rounded-lg">
        <div class="flex items-start gap-2">
          <Icon name="info" size="sm" class="text-info mt-0.5" />
          <div class="text-sm">
            <p class="text-info font-medium mb-1">About Your Data</p>
            <p class="text-base-content/70">
              We respect your privacy. You can change these preferences at any time. 
              For more information, read our 
              <router-link to="/privacy-policy" class="text-primary hover:text-primary-focus underline">
                Privacy Policy
              </router-link>.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useConsentStore } from '@/stores/consent'
import Icon from '@/components/common/Icon.vue'

// Store
const consentStore = useConsentStore()

// Local state
const localPreferences = reactive({
  analytics: false,
  marketing: false,
  functional: false
})

const originalPreferences = ref({
  analytics: false,
  marketing: false,
  functional: false
})

// Computed
const hasChanges = computed(() => {
  return localPreferences.analytics !== originalPreferences.value.analytics ||
         localPreferences.marketing !== originalPreferences.value.marketing ||
         localPreferences.functional !== originalPreferences.value.functional
})

// Methods
const formatDate = (dateString: string | null) => {
  if (!dateString) return 'Unknown'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const updatePreference = (type: keyof typeof localPreferences, value: boolean) => {
  // Just update local state, don't save immediately
  localPreferences[type] = value
}

const savePreferences = async () => {
  try {
    if (consentStore.hasConsented) {
      await consentStore.updatePreferences(localPreferences)

      // Update original preferences to reflect saved state
      originalPreferences.value = { ...localPreferences }

      console.log('✅ Consent preferences saved successfully')

      // Show success notification
      showNotification('Consent preferences saved successfully!', 'success')
    } else {
      // If user hasn't consented yet, set consent with current preferences
      consentStore.hasConsented = true
      consentStore.consentDate = new Date().toISOString()
      await consentStore.updatePreferences(localPreferences)
      consentStore.showBanner = false
      consentStore.saveConsent()

      originalPreferences.value = { ...localPreferences }

      console.log('✅ Initial consent given and preferences saved')

      // Show success notification
      showNotification('Consent preferences saved successfully!', 'success')
    }
  } catch (error) {
    console.error('Failed to save consent preferences:', error)
    showNotification('Failed to save consent preferences', 'error')
  }
}

const showConsentBanner = () => {
  consentStore.showConsentBanner()
}

const revokeAllConsent = () => {
  if (confirm('Are you sure you want to revoke all consent? This will disable analytics and other non-essential features for this session.')) {
    consentStore.revokeConsent()

    // Update local preferences
    localPreferences.analytics = false
    localPreferences.marketing = false
    localPreferences.functional = false
    originalPreferences.value = { ...localPreferences }

    showNotification('All consent revoked successfully', 'info')
  }
}

const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  // Emit a custom event for notifications
  const event = new CustomEvent('show-notification', {
    detail: { message, type, duration: 3000 }
  })
  window.dispatchEvent(event)
}

// Initialize local preferences
onMounted(() => {
  localPreferences.analytics = consentStore.preferences.analytics
  localPreferences.marketing = consentStore.preferences.marketing
  localPreferences.functional = consentStore.preferences.functional

  // Store original values for change detection
  originalPreferences.value = {
    analytics: consentStore.preferences.analytics,
    marketing: consentStore.preferences.marketing,
    functional: consentStore.preferences.functional
  }
})

// Watch for external changes to consent store
watch(() => consentStore.preferences, (newPreferences) => {
  localPreferences.analytics = newPreferences.analytics
  localPreferences.marketing = newPreferences.marketing
  localPreferences.functional = newPreferences.functional

  originalPreferences.value = { ...newPreferences }
}, { deep: true })
</script>
