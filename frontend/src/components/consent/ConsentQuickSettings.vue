<template>
  <div class="space-y-4">
    <!-- Current Status -->
    <div class="alert" :class="consentStore.hasConsented ? 'alert-success' : 'alert-warning'">
      <Icon 
        :name="consentStore.hasConsented ? 'check-circle' : 'exclamation-triangle'" 
        size="sm" 
      />
      <div>
        <h4 class="font-medium">
          {{ consentStore.hasConsented ? 'Consent Given' : 'No Consent Given' }}
        </h4>
        <p class="text-sm opacity-70">
          {{ consentStore.hasConsented 
            ? `Last updated: ${formatDate(consentStore.consentDate)}` 
            : 'You have not provided consent for data processing' 
          }}
        </p>
      </div>
    </div>

    <!-- Quick Toggle Options -->
    <div class="space-y-3">
      <h4 class="font-medium text-base-content">Quick Settings</h4>
      
      <!-- Analytics Toggle -->
      <div class="flex items-center justify-between p-3 bg-base-200/50 rounded-lg">
        <div class="flex items-center gap-2">
          <Icon name="chart-bar" size="sm" class="text-info" />
          <div>
            <span class="font-medium text-base-content">Analytics</span>
            <p class="text-xs text-base-content/70">Website usage tracking</p>
          </div>
        </div>
        <input
          type="checkbox"
          v-model="localPreferences.analytics"
          @change="updatePreference('analytics', $event.target.checked)"
          class="toggle toggle-primary toggle-sm"
        />
      </div>

      <!-- Marketing Toggle -->
      <div class="flex items-center justify-between p-3 bg-base-200/50 rounded-lg">
        <div class="flex items-center gap-2">
          <Icon name="broadcast" size="sm" class="text-warning" />
          <div>
            <span class="font-medium text-base-content">Marketing</span>
            <p class="text-xs text-base-content/70">Advertising and promotions</p>
          </div>
        </div>
        <input
          type="checkbox"
          v-model="localPreferences.marketing"
          @change="updatePreference('marketing', $event.target.checked)"
          class="toggle toggle-primary toggle-sm"
        />
      </div>

      <!-- Functional Toggle -->
      <div class="flex items-center justify-between p-3 bg-base-200/50 rounded-lg">
        <div class="flex items-center gap-2">
          <Icon name="cog" size="sm" class="text-secondary" />
          <div>
            <span class="font-medium text-base-content">Functional</span>
            <p class="text-xs text-base-content/70">Enhanced features</p>
          </div>
        </div>
        <input
          type="checkbox"
          v-model="localPreferences.functional"
          @change="updatePreference('functional', $event.target.checked)"
          class="toggle toggle-primary toggle-sm"
        />
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="flex flex-col sm:flex-row gap-2">
      <button
        @click="acceptAll"
        class="btn btn-success btn-sm flex-1"
      >
        <Icon name="check" size="sm" class="mr-1" />
        Accept All
      </button>
      
      <button
        @click="acceptNecessaryOnly"
        class="btn btn-outline btn-sm flex-1"
      >
        <Icon name="shield" size="sm" class="mr-1" />
        Necessary Only
      </button>
      
      <button
        @click="revokeConsent"
        class="btn btn-error btn-outline btn-sm flex-1"
      >
        <Icon name="error" size="sm" class="mr-1" />
        Revoke All
      </button>
    </div>

    <!-- Information -->
    <div class="text-center pt-2">
      <p class="text-xs text-base-content/60">
        Changes are saved automatically. For more details, see our 
        <router-link to="/privacy-policy" class="text-primary hover:text-primary-focus underline">
          Privacy Policy
        </router-link>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useConsentStore } from '@/stores/consent'
import Icon from '@/components/common/Icon.vue'

// Store
const consentStore = useConsentStore()

// Local state
const localPreferences = reactive({
  analytics: false,
  marketing: false,
  functional: false
})

// Computed
const formatDate = (dateString: string | null) => {
  if (!dateString) return 'Unknown'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Methods
const updatePreference = async (type: keyof typeof localPreferences, value: boolean) => {
  try {
    if (!consentStore.hasConsented) {
      // If user hasn't consented yet, give initial consent
      consentStore.hasConsented = true
      consentStore.consentDate = new Date().toISOString()
      consentStore.showBanner = false
    }
    
    await consentStore.updatePreferences({ [type]: value })
    showNotification(`${type} cookies ${value ? 'enabled' : 'disabled'}`, 'info')
  } catch (error) {
    console.error('Failed to update preference:', error)
    showNotification('Failed to update preference', 'error')
  }
}

const acceptAll = async () => {
  try {
    await consentStore.acceptAll()
    localPreferences.analytics = true
    localPreferences.marketing = true
    localPreferences.functional = true
    showNotification('All cookies accepted', 'success')
  } catch (error) {
    console.error('Failed to accept all:', error)
    showNotification('Failed to accept all cookies', 'error')
  }
}

const acceptNecessaryOnly = async () => {
  try {
    await consentStore.acceptNecessaryOnly()
    localPreferences.analytics = false
    localPreferences.marketing = false
    localPreferences.functional = false
    showNotification('Only necessary cookies accepted', 'info')
  } catch (error) {
    console.error('Failed to accept necessary only:', error)
    showNotification('Failed to update preferences', 'error')
  }
}

const revokeConsent = () => {
  if (confirm('Are you sure you want to revoke all consent? This will disable analytics and other non-essential features.')) {
    consentStore.revokeConsent()
    localPreferences.analytics = false
    localPreferences.marketing = false
    localPreferences.functional = false
    showNotification('All consent revoked', 'info')
  }
}

const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const event = new CustomEvent('show-notification', {
    detail: { message, type, duration: 3000 }
  })
  window.dispatchEvent(event)
}

// Initialize local preferences
onMounted(() => {
  localPreferences.analytics = consentStore.preferences.analytics
  localPreferences.marketing = consentStore.preferences.marketing
  localPreferences.functional = consentStore.preferences.functional
})

// Watch for external changes to consent store
watch(() => consentStore.preferences, (newPreferences) => {
  localPreferences.analytics = newPreferences.analytics
  localPreferences.marketing = newPreferences.marketing
  localPreferences.functional = newPreferences.functional
}, { deep: true })
</script>

<style scoped>
.toggle {
  --tglbg: oklch(var(--b2));
}

.toggle:checked {
  --tglbg: oklch(var(--p));
}
</style>
