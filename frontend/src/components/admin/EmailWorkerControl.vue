<template>
  <div class="email-worker-control space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-base-content">Email Worker Control</h2>
        <p class="text-base-content/70 mt-1">Monitor and manage the email queue system</p>
      </div>
      <button @click="refreshData" :disabled="isLoading" class="btn btn-primary btn-sm">
        <Icon name="refresh" size="sm" class="mr-2" />
        <span v-if="!isLoading">Refresh</span>
        <span v-else class="loading loading-spinner loading-sm"></span>
      </button>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Worker Status -->
      <div class="card glass-effect shadow-lg bg-base-100/70">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Worker Status</p>
              <p class="text-lg font-bold" :class="workerStatusClass">
                {{ workerStatus.isRunning ? 'Running' : 'Stopped' }}
              </p>
              <div v-if="workerStatus.processId" class="text-xs text-base-content/60">
                PID: {{ workerStatus.processId }}
              </div>
              <div v-if="workerStatus.uptime" class="text-xs text-base-content/60">
                Uptime: {{ workerStatus.uptime }}
              </div>
            </div>
            <div class="flex flex-col items-end">
              <div class="w-3 h-3 rounded-full" :class="workerStatusIndicator"></div>
              <div v-if="workerStatus.method" class="text-xs text-base-content/50 mt-1">
                {{ getDetectionMethod(workerStatus.method) }}
              </div>
            </div>
          </div>
          <p class="text-xs text-base-content/50 mt-2">
            Last: {{ formatLastProcessed(workerStatus.lastProcessed) }}
          </p>
        </div>
      </div>

      <!-- Pending Emails -->
      <div class="card glass-effect shadow-lg bg-base-100/70">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Pending</p>
              <p class="text-2xl font-bold text-warning">{{ statistics.pending || 0 }}</p>
            </div>
            <Icon name="clock" size="lg" class="text-warning" />
          </div>
        </div>
      </div>

      <!-- Sent Emails -->
      <div class="card glass-effect shadow-lg bg-base-100/70">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Sent</p>
              <p class="text-2xl font-bold text-success">{{ statistics.sent || 0 }}</p>
            </div>
            <Icon name="check" size="lg" class="text-success" />
          </div>
        </div>
      </div>

      <!-- Failed Emails -->
      <div class="card glass-effect shadow-lg bg-base-100/70">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Failed</p>
              <p class="text-2xl font-bold text-error">{{ statistics.failed || 0 }}</p>
            </div>
            <Icon name="x" size="lg" class="text-error" />
          </div>
        </div>
      </div>
    </div>

    <!-- Health Indicators -->
    <div v-if="healthIndicators" class="card glass-effect shadow-lg bg-base-100/70 mb-6">
      <div class="card-body">
        <h3 class="card-title mb-4">
          <Icon name="heart" size="sm" class="mr-2" />
          System Health
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 rounded-full" :class="healthIndicators.queueBacklog ? 'bg-warning' : 'bg-success'"></div>
            <div>
              <p class="text-sm font-medium">Queue Backlog</p>
              <p class="text-xs text-base-content/70">{{ healthIndicators.queueBacklog ? 'High' : 'Normal' }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 rounded-full" :class="healthIndicators.recentFailures > 5 ? 'bg-error' : healthIndicators.recentFailures > 0 ? 'bg-warning' : 'bg-success'"></div>
            <div>
              <p class="text-sm font-medium">Recent Failures</p>
              <p class="text-xs text-base-content/70">{{ healthIndicators.recentFailures }} in last hour</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 rounded-full" :class="healthIndicators.processingStuck ? 'bg-error' : 'bg-success'"></div>
            <div>
              <p class="text-sm font-medium">Processing</p>
              <p class="text-xs text-base-content/70">{{ healthIndicators.processingStuck ? 'Stuck emails detected' : 'Normal' }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 rounded-full" :class="healthIndicators.oldFailures ? 'bg-warning' : 'bg-success'"></div>
            <div>
              <p class="text-sm font-medium">Failed Queue</p>
              <p class="text-xs text-base-content/70">{{ healthIndicators.oldFailures ? 'Needs cleanup' : 'Clean' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Queue Management -->
    <div class="card glass-effect shadow-lg bg-base-100/70">
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title">Email Queue</h3>
          <div class="flex space-x-2">
            <button
              @click="clearFailedEmails"
              :disabled="isLoading || (statistics.failed || 0) === 0"
              class="btn btn-error btn-sm"
            >
              <Icon name="trash" size="sm" class="mr-2" />
              Clear Failed
            </button>
          </div>
        </div>

        <!-- Filters -->
        <div class="flex flex-wrap gap-4 mb-4">
          <div class="form-control">
            <select v-model="filters.status" @change="fetchQueue" class="select select-bordered select-sm">
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="sent">Sent</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="form-control flex-1 max-w-xs">
            <input 
              v-model="filters.search" 
              @input="debouncedSearch"
              type="text" 
              placeholder="Search emails..."
              class="input input-bordered input-sm"
            />
          </div>
        </div>

        <!-- Email Queue Table -->
        <div class="overflow-x-auto">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>To</th>
                <th>Subject</th>
                <th>Status</th>
                <th>Attempts</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="email in emailQueue" :key="email.id">
                <td class="font-mono text-sm">{{ email.to_email }}</td>
                <td class="max-w-xs truncate">{{ email.subject }}</td>
                <td>
                  <div class="badge" :class="getStatusBadgeClass(email.status)">
                    {{ email.status }}
                  </div>
                </td>
                <td>{{ email.attempts || 0 }}</td>
                <td class="text-sm">{{ formatDate(email.created_at) }}</td>
                <td>
                  <div class="flex space-x-1">
                    <button 
                      v-if="email.status === 'failed'"
                      @click="retryEmail(email.id)"
                      class="btn btn-xs btn-primary"
                      title="Retry"
                    >
                      <Icon name="refresh" size="xs" />
                    </button>
                    <button 
                      @click="showEmailDetails(email)"
                      class="btn btn-xs btn-ghost"
                      title="View Details"
                    >
                      <Icon name="eye" size="xs" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <div v-if="emailQueue.length === 0" class="text-center py-8 text-base-content/50">
            No emails in queue
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.totalPages > 1" class="flex justify-center mt-4">
          <div class="btn-group">
            <button 
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="btn btn-sm"
            >
              «
            </button>
            <button class="btn btn-sm btn-active">
              Page {{ pagination.page }} of {{ pagination.totalPages }}
            </button>
            <button 
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.totalPages"
              class="btn btn-sm"
            >
              »
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Details Modal -->
    <div v-if="selectedEmail" class="modal modal-open">
      <div class="modal-box max-w-2xl glass-effect bg-base-100/90">
        <h3 class="font-bold text-lg mb-4">Email Details</h3>
        
        <div class="space-y-4">
          <div>
            <label class="label">
              <span class="label-text font-medium">To:</span>
            </label>
            <p class="font-mono text-sm">{{ selectedEmail.to_email }}</p>
          </div>
          
          <div>
            <label class="label">
              <span class="label-text font-medium">Subject:</span>
            </label>
            <p>{{ selectedEmail.subject }}</p>
          </div>
          
          <div>
            <label class="label">
              <span class="label-text font-medium">Status:</span>
            </label>
            <div class="badge" :class="getStatusBadgeClass(selectedEmail.status)">
              {{ selectedEmail.status }}
            </div>
          </div>
          
          <div v-if="selectedEmail.error_message">
            <label class="label">
              <span class="label-text font-medium">Error:</span>
            </label>
            <div class="alert alert-error">
              <pre class="text-sm">{{ selectedEmail.error_message }}</pre>
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="label">
                <span class="label-text font-medium">Created:</span>
              </label>
              <p class="text-sm">{{ formatDate(selectedEmail.created_at) }}</p>
            </div>
            <div v-if="selectedEmail.sent_at || selectedEmail.failed_at">
              <label class="label">
                <span class="label-text font-medium">{{ selectedEmail.sent_at ? 'Sent' : 'Failed' }}:</span>
              </label>
              <p class="text-sm">{{ formatDate(selectedEmail.sent_at || selectedEmail.failed_at) }}</p>
            </div>
          </div>
        </div>

        <div class="modal-action">
          <button @click="selectedEmail = null" class="btn btn-ghost">Close</button>
          <button 
            v-if="selectedEmail.status === 'failed'"
            @click="retryEmail(selectedEmail.id); selectedEmail = null"
            class="btn btn-primary"
          >
            Retry Email
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { adminService } from '@/services/admin'
import Icon from '@/components/common/Icon.vue'

// Composables
const route = useRoute()
const authStore = useAuthStore()

// State
const isLoading = ref(false)
const workerStatus = ref({
  isRunning: false,
  lastProcessed: null,
  processId: null,
  uptime: null,
  method: 'unknown'
})
const statistics = ref({ pending: 0, processing: 0, completed: 0, failed: 0, total: 0 })
const healthIndicators = ref(null)
const emailQueue = ref([])
const selectedEmail = ref(null)
const pagination = ref({ page: 1, limit: 20, total: 0, totalPages: 0 })
const filters = ref({ status: '', search: '' })

// Check if we're on an admin page
const isOnAdminPage = computed(() => {
  return route.path.includes('/admin') && authStore.isAdmin
})

// Computed
const workerStatusClass = computed(() => 
  workerStatus.value.isRunning ? 'text-success' : 'text-error'
)

const workerStatusIndicator = computed(() => 
  workerStatus.value.isRunning ? 'bg-success animate-pulse' : 'bg-error'
)

// Methods
const refreshData = async () => {
  await Promise.all([
    fetchWorkerStatus(),
    fetchQueue()
  ])
}

// Rate limiting for API calls
let lastStatusFetch = 0
let consecutiveErrors = 0
const STATUS_FETCH_COOLDOWN = 30000 // 30 seconds minimum between calls
const MAX_CONSECUTIVE_ERRORS = 3
const ERROR_BACKOFF_TIME = 300000 // 5 minutes after 3 consecutive errors

// Reset error counters when API comes back online
const resetErrorCounters = () => {
  consecutiveErrors = 0
  console.log('🔄 Email worker API error counters reset')
}

// Listen for online events to reset error counters
if (typeof window !== 'undefined') {
  window.addEventListener('online', resetErrorCounters)
}

const fetchWorkerStatus = async () => {
  try {
    // Only fetch on admin pages
    if (!isOnAdminPage.value) {
      console.log('🚫 Not on admin page - skipping email worker status fetch')
      return
    }

    // Don't make requests when offline
    if (!navigator.onLine) {
      console.log('🌐 Offline - skipping email worker status fetch')
      return
    }

    // Rate limiting - prevent spamming
    const now = Date.now()
    if (now - lastStatusFetch < STATUS_FETCH_COOLDOWN) {
      console.log('⏱️ Email worker status fetch rate limited')
      return
    }

    // Backoff after consecutive errors
    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      if (now - lastStatusFetch < ERROR_BACKOFF_TIME) {
        console.log(`🚫 Email worker status fetch in backoff mode (${consecutiveErrors} consecutive errors)`)
        return
      }
      // Reset error count after backoff period
      consecutiveErrors = 0
    }

    lastStatusFetch = now
    const response = await adminService.getEmailWorkerStatus()

    console.log('Worker status response:', response)

    // The response should now have the correct structure from getRaw()
    workerStatus.value = response.data?.status || {}
    statistics.value = response.data?.statistics || {}
    healthIndicators.value = response.data?.health || null

    console.log('Parsed worker status:', workerStatus.value)
    console.log('Parsed statistics:', statistics.value)
    console.log('Parsed health indicators:', healthIndicators.value)

    // Reset error count on successful fetch
    consecutiveErrors = 0
  } catch (error: any) {
    consecutiveErrors++

    if (error.code === 'OFFLINE' || error.code === 'NETWORK_ERROR' || !navigator.onLine) {
      console.log('🌐 Network error fetching worker status - will retry when online')
    } else {
      console.error(`Error fetching worker status (${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS}):`, error)

      if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
        console.warn(`🚫 Email worker status fetch disabled for ${ERROR_BACKOFF_TIME / 60000} minutes due to consecutive errors`)
      }
    }
  }
}

const fetchQueue = async () => {
  try {
    isLoading.value = true
    const response = await adminService.getEmailQueue({
      page: pagination.value.page,
      limit: pagination.value.limit,
      status: filters.value.status,
      search: filters.value.search
    })
    emailQueue.value = response.items
    pagination.value = response.pagination
  } catch (error) {
    console.error('Error fetching email queue:', error)
  } finally {
    isLoading.value = false
  }
}

const retryEmail = async (emailId: number) => {
  try {
    await adminService.retryEmail(emailId)
    await refreshData()
    alert('Email queued for retry')
  } catch (error) {
    console.error('Error retrying email:', error)
    alert('Failed to retry email')
  }
}

const clearFailedEmails = async () => {
  if (!confirm('Are you sure you want to clear all failed emails?')) return

  try {
    const response = await adminService.clearFailedEmails()
    await refreshData()
    alert(response.message)
  } catch (error) {
    console.error('Error clearing failed emails:', error)
    alert('Failed to clear failed emails')
  }
}

const changePage = (page: number) => {
  pagination.value.page = page
  fetchQueue()
}

const showEmailDetails = (email: any) => {
  selectedEmail.value = email
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'pending': return 'badge-warning'
    case 'processing': return 'badge-info'
    case 'sent': return 'badge-success'
    case 'failed': return 'badge-error'
    case 'cancelled': return 'badge-ghost'
    default: return 'badge-ghost'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const formatLastProcessed = (dateString: string | null) => {
  if (!dateString) return 'Never'
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

const getDetectionMethod = (method: string) => {
  switch (method) {
    case 'pid_file': return 'PID File'
    case 'process_name': return 'Process'
    case 'recent_activity': return 'Activity'
    default: return 'Unknown'
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    pagination.value.page = 1
    fetchQueue()
  }, 500)
}

// Initialize
onMounted(() => {
  refreshData()

  // Auto-refresh every 60 seconds (increased from 30s to reduce load)
  // Only when component is visible, on admin page, and not in error backoff
  const interval = setInterval(() => {
    // Only fetch if online, on admin page, component is still mounted, and not in backoff
    if (navigator.onLine && isOnAdminPage.value && consecutiveErrors < MAX_CONSECUTIVE_ERRORS) {
      fetchWorkerStatus()
    } else if (!navigator.onLine) {
      console.log('🌐 Offline - skipping email worker status check')
    } else if (!isOnAdminPage.value) {
      console.log('🚫 Not on admin page - skipping email worker status check')
    } else {
      console.log('🚫 Skipping email worker status check - in error backoff mode')
    }
  }, 60000) // Increased to 60 seconds

  // Cleanup on unmount
  return () => clearInterval(interval)
})
</script>

<style scoped>
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}
</style>
