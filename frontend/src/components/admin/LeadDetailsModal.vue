<template>
  <div v-if="isOpen" class="modal modal-open">
    <div class="modal-box w-11/12 max-w-2xl">
      <div class="flex justify-between items-center mb-4">
        <h3 class="font-bold text-lg">{{ t('leads.modal.title') }}</h3>
        <button @click="closeModal" class="btn btn-sm btn-circle btn-ghost">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>

      <div v-else-if="lead" class="space-y-6">
        <!-- Lead Information -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h4 class="card-title text-primary">{{ t('leads.contact_information') }}</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="label">
                  <span class="label-text font-semibold">{{ t('leads.fields.name') }}</span>
                </label>
                <div class="flex items-center space-x-2">
                  <div class="avatar placeholder">
                    <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                      <span class="text-xs">{{ getInitials(lead.name) }}</span>
                    </div>
                  </div>
                  <span class="text-base">{{ lead.name }}</span>
                </div>
              </div>
              <div>
                <label class="label">
                  <span class="label-text font-semibold">{{ t('leads.fields.email') }}</span>
                </label>
                <a :href="`mailto:${lead.email}`" class="link link-primary">{{ lead.email }}</a>
              </div>
              <div>
                <label class="label">
                  <span class="label-text font-semibold">{{ t('leads.fields.phone') }}</span>
                </label>
                <a :href="`tel:${lead.phone}`" class="link link-primary">{{ lead.phone }}</a>
              </div>
              <div>
                <label class="label">
                  <span class="label-text font-semibold">{{ t('leads.lead_status') }}</span>
                </label>
                <select v-model="lead.status" @change="updateStatus" class="select select-bordered select-sm">
                  <option value="new">{{ t('leads.status.new') }}</option>
                  <option value="in_progress">{{ t('leads.status.contacted') }}</option>
                  <option value="contacted">{{ t('leads.status.contacted') }}</option>
                  <option value="qualified">{{ t('leads.status.qualified') }}</option>
                  <option value="resolved">{{ t('leads.status.qualified') }}</option>
                  <option value="follow_up">{{ t('leads.status.follow_up') }}</option>
                  <option value="interested">{{ t('leads.status.interested') }}</option>
                  <option value="not_interested">{{ t('leads.status.not_interested') }}</option>
                  <option value="callback_requested">{{ t('leads.status.callback_requested') }}</option>
                  <option value="meeting_scheduled">{{ t('leads.status.meeting_scheduled') }}</option>
                  <option value="on_hold">{{ t('leads.status.on_hold') }}</option>
                  <option value="closed">{{ t('leads.status.closed') }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Message -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h4 class="card-title text-primary">{{ t('leads.fields.message') }}</h4>
            <p class="text-base whitespace-pre-wrap">{{ lead.message }}</p>
          </div>
        </div>

        <!-- Timeline -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h4 class="card-title text-primary">{{ t('leads.timeline') }}</h4>
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="badge badge-primary">{{ t('leads.created_at') }}</div>
                <span>{{ formatDate(lead.created_at) }}</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="badge badge-secondary">{{ t('leads.updated_at') }}</div>
                <span>{{ formatDate(lead.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h4 class="card-title text-primary">{{ t('dashboard.quick_actions') }}</h4>
            <div class="flex flex-wrap gap-2">
              <button @click="sendEmail" class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                {{ t('leads.actions.send_email') }}
              </button>
              <button @click="makeCall" class="btn btn-secondary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                {{ t('leads.actions.call') }}
              </button>
              <button @click="showConvertModal = true" class="btn btn-accent btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                {{ t('leads.actions.convert') }}
              </button>
              <button @click="deleteLead" class="btn btn-error btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {{ t('leads.actions.delete') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <p class="text-error">{{ t('leads.modal.error') }}</p>
      </div>
    </div>
    <div class="modal-backdrop" @click="closeModal"></div>
  </div>

  <!-- Convert to Lead Confirmation Modal -->
  <div v-if="showConvertModal" class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg">{{ t('leads.modal.convert_title') }}</h3>
      <div class="py-4">
        <p class="mb-4">{{ t('leads.modal.convert_message', { name: lead?.name }) }}</p>
        <div class="bg-base-200 p-4 rounded-lg">
          <h4 class="font-semibold mb-2">{{ t('leads.modal.convert_description') }}</h4>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li v-for="action in t('leads.modal.convert_actions')" :key="action">{{ action }}</li>
          </ul>
        </div>
      </div>
      <div class="modal-action">
        <button @click="showConvertModal = false" class="btn btn-ghost">{{ t('leads.modal.cancel') }}</button>
        <button @click="confirmConvertToLead" class="btn btn-accent" :disabled="converting">
          <span v-if="converting" class="loading loading-spinner loading-sm mr-2"></span>
          {{ converting ? t('leads.modal.converting') : t('leads.modal.convert_button') }}
        </button>
      </div>
    </div>
    <div class="modal-backdrop" @click="showConvertModal = false"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { contactService } from '@/services/contact'
import { leadService } from '@/services/lead'

const { t } = useI18n()

interface Lead {
  id: number
  name: string
  email: string
  phone: string
  message: string
  status: string
  created_at: string
  updated_at: string
}

interface Props {
  isOpen: boolean
  leadId: number | null
}

interface Emits {
  (e: 'close'): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const lead = ref<Lead | null>(null)
const loading = ref(false)
const showConvertModal = ref(false)
const converting = ref(false)

// Watch for modal open/close and leadId changes
watch([() => props.isOpen, () => props.leadId], async ([isOpen, leadId]) => {
  if (isOpen && leadId) {
    await fetchLeadDetails(leadId)
  } else {
    lead.value = null
  }
})

const fetchLeadDetails = async (id: number) => {
  try {
    loading.value = true
    lead.value = await contactService.getContactSubmission(id)
  } catch (error) {
    console.error('Failed to fetch lead details:', error)
    lead.value = null
  } finally {
    loading.value = false
  }
}

const closeModal = () => {
  emit('close')
}

// Map new status values to backend-compatible values
const mapStatusToBackend = (status: string): string => {
  const statusMap: Record<string, string> = {
    'new': 'new',
    'contacted': 'in_progress',
    'in_progress': 'in_progress',
    'qualified': 'resolved',
    'resolved': 'resolved',
    'follow_up': 'in_progress',
    'interested': 'in_progress',
    'not_interested': 'resolved',
    'callback_requested': 'in_progress',
    'meeting_scheduled': 'in_progress',
    'on_hold': 'in_progress',
    'closed': 'resolved'
  }
  return statusMap[status] || 'new'
}

const updateStatus = async () => {
  if (!lead.value) return

  try {
    // Map the frontend status to backend-compatible status
    const backendStatus = mapStatusToBackend(lead.value.status)

    await contactService.updateContactSubmission(lead.value.id, {
      status: backendStatus
    })
    emit('updated')
  } catch (error) {
    console.error('Failed to update status:', error)
  }
}

const sendEmail = () => {
  if (lead.value) {
    window.open(`mailto:${lead.value.email}?subject=Re: Your inquiry&body=Hello ${lead.value.name},`)
  }
}

const makeCall = () => {
  if (lead.value) {
    window.open(`tel:${lead.value.phone}`)
  }
}

const confirmConvertToLead = async () => {
  if (!lead.value) return

  try {
    converting.value = true
    console.log('Converting contact submission to lead:', lead.value.id)

    // Use the lead service to create a lead from the contact submission
    const newLead = await leadService.createLeadFromContact(lead.value.id)

    console.log('✅ Successfully converted to lead:', newLead)

    // Close modals and show success
    showConvertModal.value = false
    closeModal()

    // Show success message
    alert(`Successfully converted ${lead.value.name} to qualified lead #${newLead.id}!`)

    emit('updated')
  } catch (error: any) {
    console.error('Failed to convert to lead:', error)
    showConvertModal.value = false
    alert(`Failed to convert to qualified lead: ${error.message || 'Unknown error'}`)
  } finally {
    converting.value = false
  }
}

const deleteLead = async () => {
  if (!lead.value) return
  
  if (confirm(`Are you sure you want to delete the lead from ${lead.value.name}?`)) {
    try {
      await contactService.deleteContactSubmission(lead.value.id)
      emit('updated')
      closeModal()
    } catch (error) {
      console.error('Failed to delete lead:', error)
    }
  }
}

const getInitials = (name: string) => {
  if (!name) return 'UN'
  const nameParts = name.trim().split(' ')
  const firstName = nameParts[0] || ''
  const lastName = nameParts[1] || ''
  return `${firstName.charAt(0) || ''}${lastName.charAt(0) || ''}`.toUpperCase()
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'Unknown'
  return new Date(dateString).toLocaleString()
}
</script>
