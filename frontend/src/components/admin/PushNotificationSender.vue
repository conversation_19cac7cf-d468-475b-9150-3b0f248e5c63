<template>
  <div class="card bg-base-100 shadow-lg border border-base-200/50">
    <div class="card-header border-b border-base-200/50 p-6">
      <div class="flex items-center gap-3">
        <div class="avatar">
          <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <Icon name="bell" size="lg" class="text-primary" />
          </div>
        </div>
        <div>
          <h2 class="card-title">Push Notification Sender</h2>
          <p class="text-sm text-base-content/70">Send notifications to specific users or groups</p>
        </div>
      </div>
    </div>

    <div class="card-body p-6 space-y-6">
      <!-- Target Selection -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Send To</span>
        </label>
        <div class="flex flex-wrap gap-2">
          <label class="label cursor-pointer gap-2">
            <input
              type="radio"
              name="target"
              value="user"
              v-model="form.target"
              class="radio radio-primary radio-sm"
            />
            <span class="label-text">Specific User</span>
          </label>
          <label class="label cursor-pointer gap-2">
            <input
              type="radio"
              name="target"
              value="all"
              v-model="form.target"
              class="radio radio-primary radio-sm"
            />
            <span class="label-text">All Users</span>
          </label>
          <label class="label cursor-pointer gap-2">
            <input
              type="radio"
              name="target"
              value="admins"
              v-model="form.target"
              class="radio radio-primary radio-sm"
            />
            <span class="label-text">Admin Users</span>
          </label>
        </div>
      </div>

      <!-- User Selection (when target is 'user') -->
      <div v-if="form.target === 'user'" class="form-control">
        <label class="label">
          <span class="label-text font-medium">Select User</span>
        </label>
        <select
          v-model="form.userId"
          class="select select-bordered w-full"
          :disabled="isLoadingUsers"
        >
          <option value="">Select a user...</option>
          <option
            v-for="user in users"
            :key="user.id"
            :value="user.id"
          >
            {{ user.name }} ({{ user.email }}) - {{ user.role }}
          </option>
        </select>
        <div v-if="isLoadingUsers" class="label">
          <span class="label-text-alt">
            <span class="loading loading-spinner loading-xs"></span>
            Loading users...
          </span>
        </div>
      </div>

      <!-- Notification Content -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">Title</span>
          </label>
          <input
            type="text"
            v-model="form.title"
            placeholder="Notification title"
            class="input input-bordered w-full"
            maxlength="100"
          />
          <div class="label">
            <span class="label-text-alt">{{ form.title.length }}/100</span>
          </div>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">Tag</span>
          </label>
          <input
            type="text"
            v-model="form.tag"
            placeholder="notification-tag"
            class="input input-bordered w-full"
          />
          <div class="label">
            <span class="label-text-alt">Used for grouping notifications</span>
          </div>
        </div>
      </div>

      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Message</span>
        </label>
        <textarea
          v-model="form.body"
          placeholder="Notification message"
          class="textarea textarea-bordered w-full h-24"
          maxlength="300"
        ></textarea>
        <div class="label">
          <span class="label-text-alt">{{ form.body.length }}/300</span>
        </div>
      </div>

      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Action URL (optional)</span>
        </label>
        <input
          type="url"
          v-model="form.url"
          placeholder="/dashboard or https://example.com"
          class="input input-bordered w-full"
        />
        <div class="label">
          <span class="label-text-alt">Where to navigate when notification is clicked</span>
        </div>
      </div>

      <!-- Options -->
      <div class="form-control">
        <label class="label cursor-pointer">
          <span class="label-text">Require user interaction</span>
          <input
            type="checkbox"
            v-model="form.requireInteraction"
            class="checkbox checkbox-primary"
          />
        </label>
        <div class="label">
          <span class="label-text-alt">Notification stays visible until user interacts</span>
        </div>
      </div>

      <!-- Quick Templates -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Quick Templates</span>
        </label>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="template in templates"
            :key="template.name"
            @click="applyTemplate(template)"
            class="btn btn-outline btn-sm"
          >
            {{ template.name }}
          </button>
        </div>
      </div>

      <!-- Preview -->
      <div v-if="form.title || form.body" class="card bg-base-200/50 border border-base-300/50">
        <div class="card-body p-4">
          <h4 class="font-medium text-sm text-base-content/70 mb-2">Preview</h4>
          <div class="flex items-start gap-3 p-3 bg-base-100 rounded-lg border">
            <img src="/hl-energy-logo-96w.png" alt="HL Energy" class="w-8 h-8 rounded" />
            <div class="flex-1 min-w-0">
              <div class="font-medium text-sm">{{ form.title || 'Notification Title' }}</div>
              <div class="text-sm text-base-content/70 mt-1">{{ form.body || 'Notification message will appear here...' }}</div>
              <div v-if="form.url" class="text-xs text-primary mt-1">{{ form.url }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="card-actions justify-end gap-2">
        <button
          @click="resetForm"
          class="btn btn-ghost"
          :disabled="isSending"
        >
          Reset
        </button>
        <button
          @click="sendNotification"
          class="btn btn-primary"
          :disabled="!canSend || isSending"
        >
          <span v-if="isSending" class="loading loading-spinner loading-sm"></span>
          <Icon v-else name="send" size="sm" />
          {{ isSending ? 'Sending...' : 'Send Notification' }}
        </button>
      </div>

      <!-- Result -->
      <div v-if="result" class="alert" :class="result.success ? 'alert-success' : 'alert-error'">
        <Icon :name="result.success ? 'check-circle' : 'x-circle'" size="sm" />
        <div>
          <div class="font-medium">{{ result.message }}</div>
          <div v-if="result.details" class="text-sm opacity-75">{{ result.details }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

const authStore = useAuthStore()

// Form state
const form = ref({
  target: 'user',
  userId: '',
  title: '',
  body: '',
  tag: '',
  url: '',
  requireInteraction: true
})

// Component state
const users = ref<any[]>([])
const isLoadingUsers = ref(false)
const isSending = ref(false)
const result = ref<{ success: boolean; message: string; details?: string } | null>(null)

// Computed
const canSend = computed(() => {
  const hasContent = form.value.title.trim() && form.value.body.trim()
  const hasTarget = form.value.target === 'all' || form.value.target === 'admins' || 
                   (form.value.target === 'user' && form.value.userId)
  return hasContent && hasTarget
})

// Templates
const templates = [
  {
    name: 'Welcome',
    title: '🎉 Welcome to HL Energy',
    body: 'Welcome to our energy consultation platform! We\'re here to help you optimize your energy usage.',
    tag: 'welcome',
    url: '/dashboard'
  },
  {
    name: 'New Lead',
    title: '🎯 New Lead Received',
    body: 'A new potential customer has submitted an inquiry. Check the dashboard for details.',
    tag: 'new-lead',
    url: '/dashboard/leads'
  },
  {
    name: 'System Alert',
    title: '⚠️ System Maintenance',
    body: 'Scheduled maintenance will begin in 30 minutes. Please save your work.',
    tag: 'system-alert',
    url: '/dashboard',
    requireInteraction: true
  },
  {
    name: 'Energy Alert',
    title: '⚡ Energy Usage Alert',
    body: 'Your energy consumption is higher than usual today. Check your dashboard for optimization tips.',
    tag: 'energy-alert',
    url: '/dashboard/energy'
  }
]

// Load users
const loadUsers = async () => {
  try {
    isLoadingUsers.value = true
    const response = await fetch(`${API_BASE_URL}/api/v1/push/users`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      users.value = data.data || []
    }
  } catch (error) {
    console.error('Failed to load users:', error)
  } finally {
    isLoadingUsers.value = false
  }
}

// Apply template
const applyTemplate = (template: any) => {
  form.value.title = template.title
  form.value.body = template.body
  form.value.tag = template.tag
  form.value.url = template.url
  if (template.requireInteraction !== undefined) {
    form.value.requireInteraction = template.requireInteraction
  }
}

// Send notification
const sendNotification = async () => {
  try {
    isSending.value = true
    result.value = null

    let endpoint = '/api/v1/push/send/'
    let payload: any = {
      title: form.value.title,
      body: form.value.body,
      tag: form.value.tag || 'custom-notification',
      url: form.value.url || '/dashboard',
      requireInteraction: form.value.requireInteraction
    }

    // Determine endpoint based on target
    if (form.value.target === 'user') {
      endpoint += 'user'
      payload.userId = parseInt(form.value.userId)
    } else if (form.value.target === 'admins') {
      endpoint += 'admin'
    } else if (form.value.target === 'all') {
      endpoint += 'announcement'
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify(payload)
    })

    const data = await response.json()

    if (response.ok) {
      result.value = {
        success: true,
        message: 'Notification sent successfully!',
        details: `Sent: ${data.data?.sent || 0}, Failed: ${data.data?.failed || 0}`
      }
      
      // Reset form after successful send
      setTimeout(() => {
        resetForm()
        result.value = null
      }, 3000)
    } else {
      result.value = {
        success: false,
        message: 'Failed to send notification',
        details: data.error?.message || 'Unknown error'
      }
    }
  } catch (error: any) {
    result.value = {
      success: false,
      message: 'Network error',
      details: error.message
    }
  } finally {
    isSending.value = false
  }
}

// Reset form
const resetForm = () => {
  form.value = {
    target: 'user',
    userId: '',
    title: '',
    body: '',
    tag: '',
    url: '',
    requireInteraction: true
  }
}

// Initialize
onMounted(() => {
  loadUsers()
})
</script>
