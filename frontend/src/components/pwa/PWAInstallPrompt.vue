<template>
  <!-- PWA Install Prompt Modal -->
  <div
    v-if="showInstallPrompt && canInstall"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    @click.self="hideInstallDialog"
  >
    <div class="card bg-base-100 shadow-2xl max-w-md w-full border border-base-200/50">
      <div class="card-body">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-4">
          <div class="avatar">
            <div class="w-12 h-12 rounded-lg">
              <img src="/hl-energy-logo-192w.png" alt="HL Energy" />
            </div>
          </div>
          <div>
            <h3 class="card-title text-lg">Install HL Energy App</h3>
            <p class="text-sm text-base-content/70">Get the best experience</p>
          </div>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center gap-3">
            <Icon name="zap" size="sm" class="text-primary" />
            <span class="text-sm">Faster loading and offline access</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="bell" size="sm" class="text-primary" />
            <span class="text-sm">Push notifications for updates</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="smartphone" size="sm" class="text-primary" />
            <span class="text-sm">Native app experience</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="shield-check" size="sm" class="text-primary" />
            <span class="text-sm">Secure and always up-to-date</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions justify-end gap-2">
          <button
            class="btn btn-ghost btn-sm"
            @click="hideInstallDialog"
            :disabled="isInstalling"
          >
            Maybe Later
          </button>
          <button
            class="btn btn-primary btn-sm"
            @click="handleInstall"
            :disabled="isInstalling"
          >
            <span v-if="isInstalling" class="loading loading-spinner loading-xs"></span>
            <Icon v-else name="download" size="xs" />
            {{ isInstalling ? 'Installing...' : 'Install App' }}
          </button>
        </div>

        <!-- Error message -->
        <div v-if="installError" class="alert alert-error mt-4">
          <Icon name="x-circle" size="sm" />
          <span class="text-sm">{{ installError }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Push Notification Prompt (after PWA installation) -->
  <div
    v-if="shouldShowPushPrompt && isInstalled"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
  >
    <div class="card bg-base-100 shadow-2xl max-w-md w-full border border-base-200/50">
      <div class="card-body">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-4">
          <div class="avatar">
            <div class="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
              <Icon name="bell" size="lg" class="text-primary" />
            </div>
          </div>
          <div>
            <h3 class="card-title text-lg">Enable Notifications</h3>
            <p class="text-sm text-base-content/70">Stay updated with important alerts</p>
          </div>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center gap-3">
            <Icon name="zap" size="sm" class="text-warning" />
            <span class="text-sm">Energy alerts and updates</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="users" size="sm" class="text-info" />
            <span class="text-sm">New lead notifications</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="mail" size="sm" class="text-success" />
            <span class="text-sm">Important messages</span>
          </div>
          <div class="flex items-center gap-3">
            <Icon name="settings" size="sm" class="text-secondary" />
            <span class="text-sm">System announcements</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions justify-end gap-2">
          <button
            class="btn btn-ghost btn-sm"
            @click="skipNotifications"
          >
            Skip for Now
          </button>
          <button
            class="btn btn-primary btn-sm"
            @click="enableNotifications"
            :disabled="isEnablingNotifications"
          >
            <span v-if="isEnablingNotifications" class="loading loading-spinner loading-xs"></span>
            <Icon v-else name="bell" size="xs" />
            {{ isEnablingNotifications ? 'Enabling...' : 'Enable Notifications' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Install Button (floating) -->
  <div
    v-if="canInstall && !showInstallPrompt"
    class="fixed bottom-20 right-4 z-40"
  >
    <button
      class="btn btn-primary btn-circle shadow-lg"
      @click="showInstallDialog"
      title="Install HL Energy App"
    >
      <Icon name="download" size="sm" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { usePWAInstall } from '@/composables/usePWAInstall'
import { usePushNotifications } from '@/composables/usePushNotifications'
import Icon from '@/components/common/Icon.vue'

const {
  showInstallPrompt,
  canInstall,
  isInstalling,
  installError,
  isInstalled,
  shouldShowPushPrompt,
  installPWA,
  hideInstallDialog,
  showInstallDialog
} = usePWAInstall()

const {
  requestPermission,
  subscribe,
  isGranted,
  isSubscribed
} = usePushNotifications()

// Local state
const isEnablingNotifications = ref(false)
const showPushPrompt = ref(false)

// Handle PWA installation
const handleInstall = async () => {
  const success = await installPWA()
  if (success) {
    console.log('✅ PWA installed successfully')
  }
}

// Enable push notifications
const enableNotifications = async () => {
  try {
    isEnablingNotifications.value = true
    
    // Request permission
    const permissionGranted = await requestPermission()
    
    if (permissionGranted) {
      // Subscribe to push notifications
      const subscribed = await subscribe()
      
      if (subscribed) {
        console.log('✅ Push notifications enabled')
        showPushPrompt.value = false
        
        // Show success notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('🔔 Notifications Enabled!', {
            body: 'You\'ll now receive important updates from HL Energy.',
            icon: '/hl-energy-logo-192w.png',
            badge: '/hl-energy-logo-96w.png',
            tag: 'notifications-enabled'
          })
        }
      }
    }
  } catch (error) {
    console.error('Failed to enable notifications:', error)
  } finally {
    isEnablingNotifications.value = false
  }
}

// Skip notifications
const skipNotifications = () => {
  showPushPrompt.value = false
  console.log('User skipped push notifications')
}
</script>

<style scoped>
/* Custom animations for the modals */
.card {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating button animation */
.btn-circle {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>
