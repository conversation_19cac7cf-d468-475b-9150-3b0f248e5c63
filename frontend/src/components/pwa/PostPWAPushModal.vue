<template>
  <!-- Test element to verify modal is trying to show -->
  <div v-if="showModal && isDev" class="fixed top-4 left-4 z-[9999] bg-blue-500 text-white p-2 rounded">
    Push Modal Active
  </div>

  <!-- Post-PWA Push Notifications Modal -->
  <div
    v-if="showModal"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    @click.self="handleDismiss"
  >
    <div class="card bg-base-100 shadow-2xl max-w-md w-full border border-base-200/50 glass">
      <div class="card-body">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-4">
          <div class="avatar">
            <div class="w-12 h-12 rounded-lg bg-accent/20 flex items-center justify-center">
              <Icon name="bell" size="lg" class="text-accent" />
            </div>
          </div>
          <div>
            <h3 class="card-title text-lg">Stay Connected!</h3>
            <p class="text-sm text-base-content/70">Enable notifications for important updates</p>
          </div>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              <Icon name="lightning-bolt" size="sm" class="text-primary" />
            </div>
            <div>
              <p class="font-medium">Instant Updates</p>
              <p class="text-sm text-base-content/70">Get notified about energy reports and alerts</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-secondary/20 flex items-center justify-center">
              <Icon name="calendar" size="sm" class="text-secondary" />
            </div>
            <div>
              <p class="font-medium">Appointment Reminders</p>
              <p class="text-sm text-base-content/70">Never miss important consultations</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center">
              <Icon name="shield-check" size="sm" class="text-accent" />
            </div>
            <div>
              <p class="font-medium">Security Alerts</p>
              <p class="text-sm text-base-content/70">Stay informed about account activity</p>
            </div>
          </div>
        </div>

        <!-- Permission Status -->
        <div v-if="isDenied" class="alert alert-warning mb-4">
          <Icon name="exclamation-triangle" size="sm" />
          <div>
            <div class="font-medium">Notifications Blocked</div>
            <div class="text-sm">Please enable notifications in your browser settings</div>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions justify-end gap-2">
          <button
            class="btn btn-ghost"
            @click="handleDismiss"
            :disabled="isEnabling"
          >
            Not Now
          </button>
          <button
            class="btn btn-primary"
            @click="handleEnable"
            :disabled="isEnabling || isDenied"
          >
            <span v-if="isEnabling" class="loading loading-spinner loading-sm"></span>
            <Icon v-else name="bell" size="sm" />
            {{ isEnabling ? 'Enabling...' : 'Enable Notifications' }}
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="alert alert-error mt-4">
          <Icon name="x-circle" size="sm" />
          <span>{{ error }}</span>
        </div>

        <!-- Success Message -->
        <div v-if="showSuccess" class="alert alert-success mt-4">
          <Icon name="check-circle" size="sm" />
          <span>Notifications enabled successfully!</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useFirebaseMessaging } from '@/composables/useFirebaseMessaging'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  show: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  enabled: []
  dismissed: []
}>()

// Composables
const {
  requestPermission,
  subscribe,
  isPermissionGranted,
  isSubscribed,
  error: pushError
} = useFirebaseMessaging()

// Computed for compatibility
const isGranted = computed(() => isPermissionGranted.value)
const isDenied = computed(() => Notification.permission === 'denied')

// Local state
const showModal = ref(false)
const isEnabling = ref(false)
const error = ref<string | null>(null)
const showSuccess = ref(false)

// Development mode check
const isDev = computed(() => import.meta.env.DEV)

// Check if recently dismissed (within 7 days)
const wasRecentlyDismissed = () => {
  const dismissed = localStorage.getItem('push-post-pwa-dismissed')
  if (!dismissed) {
    console.log('🔍 No push dismissal record found')
    return false
  }

  const dismissedTime = parseInt(dismissed)
  const weekInMs = 7 * 24 * 60 * 60 * 1000
  const timeSinceDismissal = Date.now() - dismissedTime
  const wasRecentlyDismissed = timeSinceDismissal < weekInMs

  console.log('🔍 Push dismissal check:', {
    dismissedTime: new Date(dismissedTime).toISOString(),
    timeSinceDismissal: Math.round(timeSinceDismissal / (60 * 60 * 1000)) + ' hours',
    wasRecentlyDismissed
  })

  return wasRecentlyDismissed
}

// Watch for prop changes
watch(() => props.show, (newValue) => {
  const shouldShow = newValue &&
                     !isSubscribed.value &&
                     !wasRecentlyDismissed()

  console.log('🔍 PostPWAPushModal watch conditions:', {
    propsShow: newValue,
    isGranted: isGranted.value,
    isSubscribed: isSubscribed.value,
    wasRecentlyDismissed: wasRecentlyDismissed(),
    shouldShow,
    finalShowModal: shouldShow
  })

  showModal.value = shouldShow
}, { immediate: true })

// Watch for successful subscription
watch(isSubscribed, (newValue) => {
  if (newValue && showModal.value) {
    showSuccess.value = true
    setTimeout(() => {
      showModal.value = false
      emit('enabled')
      emit('close')
    }, 2000)
  }
})

// Methods
const handleEnable = async () => {
  try {
    isEnabling.value = true
    error.value = null
    
    // Request permission
    const permissionGranted = await requestPermission()
    
    if (permissionGranted) {
      // Subscribe to push notifications
      const subscribed = await subscribe()
      
      if (subscribed) {
        console.log('✅ Firebase push notifications enabled from post-login modal')

        // Show success notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('🔥 Firebase Notifications Enabled!', {
            body: 'You\'ll now receive important updates from HL Energy via Firebase.',
            icon: '/hl-energy-logo-192w.png',
            badge: '/hl-energy-logo-96w.png',
            tag: 'firebase-notifications-enabled'
          })
        }
      } else {
        error.value = 'Failed to subscribe to Firebase notifications'
      }
    } else {
      error.value = 'Notification permission denied'
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to enable Firebase notifications'
    console.error('❌ Firebase notification setup failed:', err)
  } finally {
    isEnabling.value = false
  }
}

const handleDismiss = () => {
  showModal.value = false
  emit('dismissed')
  emit('close')
  
  // Store dismissal to avoid showing again too soon
  localStorage.setItem('push-post-pwa-dismissed', Date.now().toString())
}

// Watch for push errors
watch(pushError, (newError) => {
  if (newError) {
    error.value = newError
  }
})
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
