<template>
  <div class="performance-dashboard p-6 bg-base-100 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">🚀 Performance Dashboard</h2>
      <div class="flex gap-2">
        <button 
          @click="toggleProfiling" 
          :class="['btn btn-sm', isEnabled ? 'btn-success' : 'btn-error']"
        >
          {{ isEnabled ? 'Disable' : 'Enable' }} Profiling
        </button>
        <button @click="clearMetrics" class="btn btn-sm btn-warning">
          Clear Data
        </button>
        <button @click="generateReport" class="btn btn-sm btn-primary">
          Generate Report
        </button>
      </div>
    </div>

    <!-- Overall Score -->
    <div class="stats shadow mb-6 w-full">
      <div class="stat">
        <div class="stat-title">Performance Score</div>
        <div class="stat-value" :class="getScoreColor(summary.overallScore)">
          {{ summary.overallScore }}%
        </div>
        <div class="stat-desc">{{ getScoreDescription(summary.overallScore) }}</div>
      </div>
      <div class="stat">
        <div class="stat-title">Critical Issues</div>
        <div class="stat-value text-error">{{ summary.criticalIssues }}</div>
        <div class="stat-desc">Issues requiring attention</div>
      </div>
      <div class="stat">
        <div class="stat-title">Metrics Tracked</div>
        <div class="stat-value text-info">{{ summary.totalMetrics }}</div>
        <div class="stat-desc">{{ summary.componentsTracked }} components</div>
      </div>
    </div>

    <!-- Web Vitals -->
    <div class="card bg-base-200 shadow-xl mb-6">
      <div class="card-body">
        <h3 class="card-title">📊 Core Web Vitals</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div 
            v-for="vital in webVitals" 
            :key="vital.metric"
            class="stat bg-base-100 rounded-lg"
          >
            <div class="stat-title">{{ vital.metric }}</div>
            <div class="stat-value" :class="getRatingColor(vital.rating)">
              {{ vital.value }}{{ vital.unit }}
            </div>
            <div class="stat-desc">{{ vital.rating }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Resource Metrics -->
    <div class="card bg-base-200 shadow-xl mb-6">
      <div class="card-body">
        <h3 class="card-title">📦 Resource Metrics</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Total Resources</div>
            <div class="stat-value text-primary">{{ resourceMetrics.totalResources }}</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Total Size</div>
            <div class="stat-value text-secondary">{{ resourceMetrics.totalSize }}KB</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Slow Resources</div>
            <div class="stat-value text-warning">{{ resourceMetrics.slowResources }}</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Avg Load Time</div>
            <div class="stat-value text-info">{{ resourceMetrics.averageLoadTime }}ms</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Component Performance -->
    <div class="card bg-base-200 shadow-xl mb-6" v-if="componentMetrics.length > 0">
      <div class="card-body">
        <h3 class="card-title">🧩 Component Performance</h3>
        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr>
                <th>Component</th>
                <th>Mount Time</th>
                <th>Render Time</th>
                <th>Updates</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="component in componentMetrics" :key="component.name">
                <td class="font-semibold">{{ component.name }}</td>
                <td>{{ Math.round(component.mountTime) }}ms</td>
                <td>{{ Math.round(component.renderTime) }}ms</td>
                <td>{{ component.updateCount }}</td>
                <td>
                  <div class="badge" :class="getComponentStatusColor(component)">
                    {{ getComponentStatus(component) }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Recommendations -->
    <div class="card bg-base-200 shadow-xl mb-6" v-if="recommendations.length > 0">
      <div class="card-body">
        <h3 class="card-title">💡 Optimization Recommendations</h3>
        <div class="space-y-2">
          <div 
            v-for="(recommendation, index) in recommendations" 
            :key="index"
            class="alert alert-warning"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>{{ recommendation }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Metrics -->
    <div class="card bg-base-200 shadow-xl">
      <div class="card-body">
        <h3 class="card-title">⚡ Real-time Metrics</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Memory Usage</div>
            <div class="stat-value text-primary">{{ memoryUsage.used }}MB</div>
            <div class="stat-desc">{{ memoryUsage.percentage }}% of {{ memoryUsage.total }}MB</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">FPS</div>
            <div class="stat-value text-secondary">{{ currentFPS }}</div>
            <div class="stat-desc">Frames per second</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { performanceProfiler } from '@/utils/performanceProfiler'

// Reactive state
const isEnabled = ref(false)
const webVitals = ref<any[]>([])
const resourceMetrics = ref<any>({})
const componentMetrics = ref<any[]>([])
const recommendations = ref<string[]>([])
const summary = ref<any>({})
const memoryUsage = ref({ used: 0, total: 0, percentage: 0 })
const currentFPS = ref(0)

// FPS tracking
let fpsFrames = 0
let fpsStartTime = performance.now()
let fpsInterval: number | null = null

const trackFPS = () => {
  fpsFrames++
  const currentTime = performance.now()
  
  if (currentTime - fpsStartTime >= 1000) {
    currentFPS.value = Math.round(fpsFrames * 1000 / (currentTime - fpsStartTime))
    fpsFrames = 0
    fpsStartTime = currentTime
  }
  
  if (isEnabled.value) {
    requestAnimationFrame(trackFPS)
  }
}

// Update metrics
const updateMetrics = () => {
  const report = performanceProfiler.generateReport()
  
  webVitals.value = report.webVitals
  resourceMetrics.value = report.resourceMetrics
  componentMetrics.value = report.componentMetrics
  recommendations.value = report.recommendations
  summary.value = report.summary

  // Update memory usage
  if (performance.memory) {
    const memory = performance.memory
    memoryUsage.value = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
      percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    }
  }
}

// Methods
const toggleProfiling = () => {
  if (isEnabled.value) {
    performanceProfiler.disable()
    isEnabled.value = false
    if (fpsInterval) {
      clearInterval(fpsInterval)
      fpsInterval = null
    }
  } else {
    performanceProfiler.enable()
    isEnabled.value = true
    requestAnimationFrame(trackFPS)
  }
}

const clearMetrics = () => {
  performanceProfiler.clear()
  updateMetrics()
}

const generateReport = () => {
  const report = performanceProfiler.generateReport()
  console.log('📊 Performance Report:', report)
  
  // Download report as JSON
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${new Date().toISOString()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// Utility functions
const getScoreColor = (score: number) => {
  if (score >= 90) return 'text-success'
  if (score >= 70) return 'text-warning'
  return 'text-error'
}

const getScoreDescription = (score: number) => {
  if (score >= 90) return 'Excellent performance'
  if (score >= 70) return 'Good performance'
  if (score >= 50) return 'Needs improvement'
  return 'Poor performance'
}

const getRatingColor = (rating: string) => {
  switch (rating) {
    case 'good': return 'text-success'
    case 'needs-improvement': return 'text-warning'
    case 'poor': return 'text-error'
    default: return 'text-base-content'
  }
}

const getComponentStatus = (component: any) => {
  if (component.renderTime > 16) return 'Slow'
  if (component.updateCount > 50) return 'Frequent Updates'
  return 'Good'
}

const getComponentStatusColor = (component: any) => {
  const status = getComponentStatus(component)
  switch (status) {
    case 'Good': return 'badge-success'
    case 'Slow': return 'badge-error'
    case 'Frequent Updates': return 'badge-warning'
    default: return 'badge-neutral'
  }
}

// Lifecycle
onMounted(() => {
  isEnabled.value = performanceProfiler.isEnabled || false
  updateMetrics()
  
  // Update metrics every 5 seconds
  const metricsInterval = setInterval(updateMetrics, 5000)
  
  onUnmounted(() => {
    clearInterval(metricsInterval)
    if (fpsInterval) {
      clearInterval(fpsInterval)
    }
  })
  
  // Start FPS tracking if enabled
  if (isEnabled.value) {
    requestAnimationFrame(trackFPS)
  }
})
</script>

<style scoped>
.performance-dashboard {
  max-height: 90vh;
  overflow-y: auto;
}

.stat {
  @apply p-4;
}

.table th {
  @apply bg-base-300;
}
</style>
