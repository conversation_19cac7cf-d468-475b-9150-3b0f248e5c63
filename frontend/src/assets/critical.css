/* Critical CSS for above-the-fold content - INLINE THIS */
/* Only the most essential styles for initial render */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
  background-color: #ffffff;
  color: #1f2937;
  min-height: 100vh;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Critical layout styles */
#app {
  min-height: 100vh;
  background-color: #ffffff;
}

/* Header styles (critical for LCP) */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e2e8f0;
  height: 4rem;
}

/* Hero section (critical for LCP) */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #02342b;
  text-align: center;
  padding: 2rem 1rem;
  margin-top: 4rem;
  position: relative;
}

.hero h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
  color: #02342b;
}

.hero p {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  margin-bottom: 2rem;
  color: #6b7280;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Button styles (critical for CTA) */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #12816c;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-height: 44px;
  min-width: 44px;
}

.btn-primary:hover {
  background-color: #5cad64;
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #eaaa34;
  color: #1a1a1a;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-height: 44px;
  min-width: 44px;
}

.btn-secondary:hover {
  background-color: #d4941e;
  transform: translateY(-1px);
}

/* Loading states */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive utilities */
@media (min-width: 640px) {
  .hero h1 {
    font-size: 3.5rem;
  }
  
  .hero p {
    font-size: 1.5rem;
  }
  
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hero h1 {
    font-size: 4rem;
  }
  
  .container {
    padding: 0 2rem;
  }
}

/* Hide non-critical content initially */
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Prevent layout shift */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
