import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { analyticsService, type AnalyticsMetrics, type BusinessMetrics, type HeatmapData } from '@/services/analytics'

export const useAnalyticsStore = defineStore('analytics', () => {
  // State
  const analyticsMetrics = ref<AnalyticsMetrics | null>(null)
  const businessMetrics = ref<BusinessMetrics | null>(null)
  const heatmapData = ref<Record<string, HeatmapData>>({})
  const conversionFunnel = ref<Array<{ step: string; users: number; conversionRate: number }>>([])

  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)
  const selectedTimeRange = ref<'24h' | '7d' | '30d' | '90d'>('7d')

  // Memory management constants
  const MAX_HEATMAP_ENTRIES = 100 // Limit heatmap data
  const MAX_FUNNEL_ENTRIES = 50 // Limit conversion funnel data
  const MEMORY_CLEANUP_INTERVAL = 5 * 60 * 1000 // 5 minutes

  // Getters
  const totalUsers = computed(() => analyticsMetrics.value?.totalUsers || 0)
  const conversionRate = computed(() => businessMetrics.value?.conversionRate || 0)
  const totalLeads = computed(() => businessMetrics.value?.totalLeads || 0)
  const qualifiedLeads = computed(() => businessMetrics.value?.qualifiedLeads || 0)
  
  const topPerformingPages = computed(() => 
    analyticsMetrics.value?.topPages?.slice(0, 5) || []
  )
  
  const leadSourcesData = computed(() => 
    businessMetrics.value?.leadSources || []
  )
  
  const serviceInterestData = computed(() => 
    businessMetrics.value?.serviceInterest || []
  )
  
  const timeSeriesData = computed(() => 
    businessMetrics.value?.timeSeriesData || []
  )

  // Real-time metrics (from Socket.io)
  const realTimeUsers = ref(0)
  const realTimePageViews = ref(0)
  const realTimeConversions = ref(0)

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTimeRange = (range: '24h' | '7d' | '30d' | '90d') => {
    selectedTimeRange.value = range
    // Automatically refresh data when time range changes
    refreshAllData()
  }

  // Fetch analytics metrics
  const fetchAnalyticsMetrics = async (timeRange?: '24h' | '7d' | '30d' | '90d') => {
    try {
      clearError()
      const range = timeRange || selectedTimeRange.value
      const data = await analyticsService.getAnalyticsMetrics(range)
      analyticsMetrics.value = data
      lastUpdated.value = new Date()
      return data
    } catch (err: any) {
      console.warn('Analytics metrics endpoint not available, using mock data:', err.message)
      // Use mock data as fallback
      const mockData: AnalyticsMetrics = {
        totalUsers: 1250,
        activeUsers: 89,
        pageViews: 5420,
        sessions: 1180,
        bounceRate: 0.32,
        avgSessionDuration: 245,
        conversionRate: 0.08,
        topPages: [
          { page: '/', views: 1200 },
          { page: '/services', views: 890 },
          { page: '/about', views: 650 },
          { page: '/contact', views: 420 }
        ],
        topReferrers: [
          { referrer: 'google.com', visits: 450 },
          { referrer: 'direct', visits: 320 },
          { referrer: 'facebook.com', visits: 180 }
        ],
        userFlow: [
          { from: '/', to: '/services', count: 340 },
          { from: '/services', to: '/contact', count: 120 }
        ]
      }
      analyticsMetrics.value = mockData
      lastUpdated.value = new Date()
      return mockData
    }
  }

  // Fetch business metrics
  const fetchBusinessMetrics = async (timeRange?: '24h' | '7d' | '30d' | '90d') => {
    try {
      clearError()
      const range = timeRange || selectedTimeRange.value
      const data = await analyticsService.getBusinessMetrics(range)
      businessMetrics.value = data
      return data
    } catch (err: any) {
      console.warn('Business metrics endpoint not available, using mock data:', err.message)
      // Use mock data as fallback
      const mockData: BusinessMetrics = {
        totalLeads: 156,
        qualifiedLeads: 89,
        conversionRate: 0.15,
        leadSources: [
          { source: 'Website', count: 78, conversionRate: 0.18 },
          { source: 'Referral', count: 45, conversionRate: 0.22 },
          { source: 'Social Media', count: 23, conversionRate: 0.12 },
          { source: 'Email Campaign', count: 10, conversionRate: 0.08 }
        ],
        serviceInterest: [
          { service: 'Energy Audit', inquiries: 67 },
          { service: 'Solar Installation', inquiries: 45 },
          { service: 'Energy Efficiency', inquiries: 32 },
          { service: 'Monitoring', inquiries: 12 }
        ],
        geographicData: [
          { region: 'Lisbon', leads: 45 },
          { region: 'Porto', leads: 32 },
          { region: 'Braga', leads: 18 }
        ],
        timeSeriesData: [
          { date: '2024-01-01', leads: 12, conversions: 3 },
          { date: '2024-01-02', leads: 15, conversions: 4 },
          { date: '2024-01-03', leads: 18, conversions: 5 }
        ]
      }
      businessMetrics.value = mockData
      return mockData
    }
  }

  // Fetch heatmap data for a specific page
  const fetchHeatmapData = async (page: string, timeRange?: '24h' | '7d' | '30d') => {
    try {
      clearError()
      const range = timeRange || '7d'
      const data = await analyticsService.getHeatmapData(page, range)
      heatmapData.value[page] = data
      return data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch heatmap data')
      throw err
    }
  }

  // Fetch conversion funnel
  const fetchConversionFunnel = async () => {
    try {
      clearError()
      const data = await analyticsService.getConversionFunnel()
      conversionFunnel.value = data
      return data
    } catch (err: any) {
      console.warn('Conversion funnel endpoint not available, using mock data:', err.message)
      // Use mock data as fallback
      const mockData = [
        { step: 'Website Visit', users: 1000, conversionRate: 100 },
        { step: 'Contact Form', users: 150, conversionRate: 15 },
        { step: 'Quote Request', users: 75, conversionRate: 7.5 },
        { step: 'Consultation', users: 45, conversionRate: 4.5 },
        { step: 'Contract Signed', users: 25, conversionRate: 2.5 }
      ]
      conversionFunnel.value = mockData
      return mockData
    }
  }

  // Refresh all dashboard data
  const refreshAllData = async () => {
    if (isLoading.value) return

    try {
      isLoading.value = true
      clearError()

      await Promise.all([
        fetchAnalyticsMetrics(),
        fetchBusinessMetrics(),
        fetchConversionFunnel()
      ])

      lastUpdated.value = new Date()
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard data')
    } finally {
      isLoading.value = false
    }
  }

  // Get dashboard summary
  const getDashboardSummary = computed(() => {
    if (!analyticsMetrics.value || !businessMetrics.value) return null

    return {
      users: {
        total: analyticsMetrics.value.totalUsers,
        active: analyticsMetrics.value.activeUsers,
        growth: calculateGrowth(analyticsMetrics.value.totalUsers, 100) // Mock previous period
      },
      leads: {
        total: businessMetrics.value.totalLeads,
        qualified: businessMetrics.value.qualifiedLeads,
        conversionRate: businessMetrics.value.conversionRate,
        growth: calculateGrowth(businessMetrics.value.totalLeads, 50) // Mock previous period
      },
      engagement: {
        pageViews: analyticsMetrics.value.pageViews,
        sessions: analyticsMetrics.value.sessions,
        bounceRate: analyticsMetrics.value.bounceRate,
        avgSessionDuration: analyticsMetrics.value.avgSessionDuration
      },
      performance: {
        topPage: analyticsMetrics.value.topPages[0]?.page || 'N/A',
        topReferrer: analyticsMetrics.value.topReferrers[0]?.referrer || 'Direct',
        bestConvertingSource: businessMetrics.value.leadSources[0]?.source || 'N/A'
      }
    }
  })

  // Calculate growth percentage
  const calculateGrowth = (current: number, previous: number): number => {
    if (previous === 0) return 0
    return Math.round(((current - previous) / previous) * 100)
  }

  // Update real-time metrics from Socket.io data
  const updateRealTimeMetrics = (socketData: any) => {
    if (socketData.activeUsers !== undefined) {
      realTimeUsers.value = socketData.activeUsers
    }
    if (socketData.pageViews !== undefined) {
      realTimePageViews.value = socketData.pageViews
    }
    if (socketData.conversions !== undefined) {
      realTimeConversions.value = socketData.conversions
    }
  }



  // Export data for reports
  const exportAnalyticsData = async (format: 'csv' | 'json' | 'pdf' = 'csv') => {
    try {
      const data = {
        analytics: analyticsMetrics.value,
        business: businessMetrics.value,
        funnel: conversionFunnel.value,
        timeRange: selectedTimeRange.value,
        exportedAt: new Date().toISOString()
      }

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        return blob
      }

      // For CSV and PDF, you would typically call a backend endpoint
      // For now, return JSON blob
      return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    } catch (err: any) {
      setError(err.message || 'Failed to export data')
      throw err
    }
  }

  // Memory management functions
  const cleanupMemory = () => {
    // Clean up heatmap data - keep only recent entries
    const heatmapKeys = Object.keys(heatmapData.value)
    if (heatmapKeys.length > MAX_HEATMAP_ENTRIES) {
      const toRemove = heatmapKeys.slice(0, heatmapKeys.length - MAX_HEATMAP_ENTRIES)
      toRemove.forEach(key => delete heatmapData.value[key])
      console.log(`🧹 Analytics: Cleaned up ${toRemove.length} old heatmap entries`)
    }

    // Clean up conversion funnel data
    if (conversionFunnel.value.length > MAX_FUNNEL_ENTRIES) {
      const removed = conversionFunnel.value.splice(0, conversionFunnel.value.length - MAX_FUNNEL_ENTRIES)
      console.log(`🧹 Analytics: Cleaned up ${removed.length} old funnel entries`)
    }

    // Memory cleanup completed - let browser handle GC naturally
  }

  const resetAnalyticsData = () => {
    analyticsMetrics.value = null
    businessMetrics.value = null
    heatmapData.value = {}
    conversionFunnel.value = []
    realTimeUsers.value = 0
    realTimePageViews.value = 0
    realTimeConversions.value = 0
    console.log('🧹 Analytics: All data reset')
  }

  // Initialize analytics store
  const initializeAnalyticsStore = async () => {
    await refreshAllData()

    // Set up periodic memory cleanup
    setInterval(cleanupMemory, MEMORY_CLEANUP_INTERVAL)
    console.log('🧹 Analytics: Memory cleanup scheduled every 5 minutes')
  }

  return {
    // State
    analyticsMetrics,
    businessMetrics,
    heatmapData,
    conversionFunnel,
    isLoading,
    error,
    lastUpdated,
    selectedTimeRange,
    realTimeUsers,
    realTimePageViews,
    realTimeConversions,

    // Getters
    totalUsers,
    conversionRate,
    totalLeads,
    qualifiedLeads,
    topPerformingPages,
    leadSourcesData,
    serviceInterestData,
    timeSeriesData,
    getDashboardSummary,

    // Actions
    setTimeRange,
    fetchAnalyticsMetrics,
    fetchBusinessMetrics,
    fetchHeatmapData,
    fetchConversionFunnel,
    refreshAllData,
    updateRealTimeMetrics,
    exportAnalyticsData,
    initializeAnalyticsStore,
    setError,
    clearError,

    // Memory management
    cleanupMemory,
    resetAnalyticsData
  }
})
