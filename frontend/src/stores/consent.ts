import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ConsentPreferences {
  necessary: boolean // Always true, cannot be disabled
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export interface ConsentState {
  hasConsented: boolean
  consentDate: string | null
  preferences: ConsentPreferences
  version: string
}

const CONSENT_VERSION = '1.0.0'
const CONSENT_STORAGE_KEY = 'hlenergy_consent'
const CONSENT_VERSION_KEY = 'hlenergy_consent_version'

export const useConsentStore = defineStore('consent', () => {
  // State
  const hasConsented = ref(false)
  const consentDate = ref<string | null>(null)
  const preferences = ref<ConsentPreferences>({
    necessary: true, // Always true
    analytics: false,
    marketing: false,
    functional: false
  })
  const version = ref(CONSENT_VERSION)
  const showBanner = ref(false)

  // Computed
  const canUseAnalytics = computed(() => hasConsented.value && preferences.value.analytics)
  const canUseMarketing = computed(() => hasConsented.value && preferences.value.marketing)
  const canUseFunctional = computed(() => hasConsented.value && preferences.value.functional)
  
  const isConsentRequired = computed(() => !hasConsented.value || shouldUpdateConsent.value)
  
  const shouldUpdateConsent = computed(() => {
    if (!hasConsented.value) return true
    
    // Check if consent version has changed
    const storedVersion = localStorage.getItem(`${CONSENT_STORAGE_KEY}_version`)
    return storedVersion !== CONSENT_VERSION
  })

  // Actions
  const loadConsent = () => {
    try {
      // Check sessionStorage first (current session)
      const sessionStored = sessionStorage.getItem(CONSENT_STORAGE_KEY)
      if (sessionStored) {
        const consentData: ConsentState = JSON.parse(sessionStored)

        // Session storage is always valid for current session
        if (consentData.version === CONSENT_VERSION) {
          hasConsented.value = consentData.hasConsented
          consentDate.value = consentData.consentDate
          preferences.value = { ...consentData.preferences }
          version.value = consentData.version
          showBanner.value = false
          console.log('📋 Consent loaded from session storage')
          return
        }
      }

      // Fallback to localStorage for persistent preferences
      const stored = localStorage.getItem(CONSENT_STORAGE_KEY)
      if (stored) {
        const consentData: ConsentState = JSON.parse(stored)

        // Check if consent is still valid (not expired and same version)
        const consentAge = Date.now() - new Date(consentData.consentDate || 0).getTime()
        const maxAge = 365 * 24 * 60 * 60 * 1000 // 1 year in milliseconds

        if (consentAge < maxAge && consentData.version === CONSENT_VERSION) {
          hasConsented.value = consentData.hasConsented
          consentDate.value = consentData.consentDate
          preferences.value = { ...consentData.preferences }
          version.value = consentData.version
          showBanner.value = false

          // Also save to session storage for this session
          saveToSessionStorage()
          console.log('📋 Consent loaded from localStorage and copied to session')
        } else {
          // Consent expired or version changed, show banner
          showBanner.value = true
        }
      } else {
        // No consent found, show banner
        showBanner.value = true
      }
    } catch (error) {
      console.warn('Failed to load consent preferences:', error)
      showBanner.value = true
    }
  }

  const saveToSessionStorage = () => {
    const consentData: ConsentState = {
      hasConsented: hasConsented.value,
      consentDate: consentDate.value,
      preferences: { ...preferences.value },
      version: version.value
    }

    try {
      sessionStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData))
      sessionStorage.setItem(CONSENT_VERSION_KEY, version.value)
    } catch (error) {
      console.warn('Failed to save consent to session storage:', error)
    }
  }

  const saveToLocalStorage = () => {
    const consentData: ConsentState = {
      hasConsented: hasConsented.value,
      consentDate: consentDate.value,
      preferences: { ...preferences.value },
      version: version.value
    }

    try {
      localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData))
      localStorage.setItem(CONSENT_VERSION_KEY, version.value)
    } catch (error) {
      console.warn('Failed to save consent to localStorage:', error)
    }
  }

  const saveConsent = () => {
    // Save to both session and local storage
    saveToSessionStorage()
    saveToLocalStorage()
  }

  const acceptAll = async () => {
    hasConsented.value = true
    consentDate.value = new Date().toISOString()
    preferences.value = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true
    }
    showBanner.value = false
    saveConsent()

    // Immediately initialize analytics if consent includes analytics
    if (preferences.value.analytics) {
      try {
        // Dynamic import to avoid circular dependencies
        const { reinitializeAnalytics } = await import('@/plugins/firebase')
        await reinitializeAnalytics()
        console.log('🚀 Analytics initialized immediately after consent')
      } catch (error) {
        console.warn('Failed to initialize analytics immediately:', error)
      }
    }

    // Emit event for other parts of the app
    window.dispatchEvent(new CustomEvent('consent-updated', {
      detail: { preferences: preferences.value }
    }))
  }

  const acceptNecessaryOnly = async () => {
    hasConsented.value = true
    consentDate.value = new Date().toISOString()
    preferences.value = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false
    }
    showBanner.value = false
    saveConsent()

    // Emit event for other parts of the app
    window.dispatchEvent(new CustomEvent('consent-updated', {
      detail: { preferences: preferences.value }
    }))
  }

  const updatePreferences = async (newPreferences: Partial<ConsentPreferences>) => {
    const oldAnalyticsConsent = preferences.value.analytics

    preferences.value = {
      ...preferences.value,
      ...newPreferences,
      necessary: true // Always keep necessary cookies enabled
    }

    if (hasConsented.value) {
      saveConsent()

      // If analytics consent changed from false to true, initialize immediately
      if (!oldAnalyticsConsent && preferences.value.analytics) {
        try {
          const { reinitializeAnalytics } = await import('@/plugins/firebase')
          await reinitializeAnalytics()
          console.log('🚀 Analytics initialized immediately after preference update')
        } catch (error) {
          console.warn('Failed to initialize analytics immediately:', error)
        }
      }

      // Emit event for other parts of the app
      window.dispatchEvent(new CustomEvent('consent-updated', {
        detail: { preferences: preferences.value }
      }))
    }
  }

  const revokeConsent = () => {
    hasConsented.value = false
    consentDate.value = null
    preferences.value = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false
    }
    showBanner.value = true

    // Clear stored consent from both storages
    sessionStorage.removeItem(CONSENT_STORAGE_KEY)
    sessionStorage.removeItem(CONSENT_VERSION_KEY)
    localStorage.removeItem(CONSENT_STORAGE_KEY)
    localStorage.removeItem(CONSENT_VERSION_KEY)

    // Emit event for other parts of the app
    window.dispatchEvent(new CustomEvent('consent-revoked'))
  }

  const hideBanner = () => {
    showBanner.value = false
  }

  const showConsentBanner = () => {
    showBanner.value = true
  }

  // Initialize on store creation
  loadConsent()

  return {
    // State
    hasConsented,
    consentDate,
    preferences,
    version,
    showBanner,
    
    // Computed
    canUseAnalytics,
    canUseMarketing,
    canUseFunctional,
    isConsentRequired,
    shouldUpdateConsent,
    
    // Actions
    loadConsent,
    saveConsent,
    acceptAll,
    acceptNecessaryOnly,
    updatePreferences,
    revokeConsent,
    hideBanner,
    showConsentBanner
  }
})
