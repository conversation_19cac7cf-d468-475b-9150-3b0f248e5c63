<template>
  <div class="socket-status">
    <!-- Compact Status Indicator -->
    <div 
      v-if="!expanded"
      class="flex items-center space-x-2 cursor-pointer"
      @click="expanded = true"
    >
      <div 
        class="w-2 h-2 rounded-full transition-colors duration-300"
        :class="{
          'bg-success animate-pulse': status.isConnected,
          'bg-warning animate-pulse': status.isConnecting,
          'bg-error': status.status === 'error',
          'bg-neutral': status.status === 'disconnected'
        }"
      ></div>
      <span class="text-xs text-base-content/70">{{ status.getStatusText() }}</span>
    </div>

    <!-- Expanded Status Panel -->
    <div 
      v-else
      class="bg-base-200 rounded-lg p-4 shadow-lg border border-base-300 min-w-80"
    >
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-semibold flex items-center">
          <Icon :name="status.getStatusIcon()" size="sm" class="mr-2" />
          Real-time Connection
        </h3>
        <button 
          @click="expanded = false"
          class="btn btn-ghost btn-xs"
        >
          <Icon name="x" size="sm" />
        </button>
      </div>

      <!-- Connection Status -->
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm">Status:</span>
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="{
                'bg-success animate-pulse': status.isConnected,
                'bg-warning animate-pulse': status.isConnecting,
                'bg-error': status.status === 'error',
                'bg-neutral': status.status === 'disconnected'
              }"
            ></div>
            <span 
              class="text-sm font-medium"
              :class="{
                'text-success': status.isConnected,
                'text-warning': status.isConnecting,
                'text-error': status.status === 'error',
                'text-neutral': status.status === 'disconnected'
              }"
            >
              {{ status.getStatusText() }}
            </span>
          </div>
        </div>

        <!-- Connection Details -->
        <div v-if="status.isConnected" class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span>Server:</span>
            <span class="font-mono text-xs">{{ serverUrl }}</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span>Transport:</span>
            <span class="badge badge-sm badge-primary">WebSocket</span>
          </div>
          
          <div v-if="lastPing" class="flex items-center justify-between text-sm">
            <span>Last Ping:</span>
            <span class="text-xs">{{ formatPingTime(lastPing) }}</span>
          </div>
        </div>

        <!-- Error Details -->
        <div v-if="status.error" class="space-y-2">
          <div class="text-sm text-error">
            <strong>Error:</strong> {{ status.error }}
          </div>
          
          <div v-if="status.reconnectAttempts > 0" class="text-sm">
            <strong>Reconnect Attempts:</strong> {{ status.reconnectAttempts }}
          </div>
        </div>

        <!-- Connection Actions -->
        <div class="flex space-x-2 pt-2">
          <button 
            v-if="!status.isConnected && !status.isConnecting"
            @click="status.connect()"
            class="btn btn-primary btn-sm flex-1"
          >
            <Icon name="refresh" size="sm" class="mr-1" />
            Connect
          </button>
          
          <button 
            v-if="status.isConnected"
            @click="status.disconnect()"
            class="btn btn-error btn-sm flex-1"
          >
            <Icon name="wifi-off" size="sm" class="mr-1" />
            Disconnect
          </button>
          
          <button 
            v-if="status.isConnecting"
            disabled
            class="btn btn-warning btn-sm flex-1"
          >
            <span class="loading loading-spinner loading-sm mr-1"></span>
            Connecting...
          </button>
        </div>

        <!-- Real-time Features Status -->
        <div class="pt-3 border-t border-base-300">
          <h4 class="text-sm font-medium mb-2">Real-time Features</h4>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div class="flex items-center space-x-1">
              <Icon 
                name="chart-line" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Analytics</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="bell" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Notifications</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="message-circle" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Chat</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="activity" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Live Updates</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useSocketStatus } from '@/plugins/socket'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  autoExpand?: boolean
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoExpand: false,
  showDetails: true
})

// State
const expanded = ref(props.autoExpand)
const status = useSocketStatus()

// Computed
const serverUrl = computed(() => {
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
})

const lastPing = computed(() => {
  return status.lastPing?.value
})

// Methods
const formatPingTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 1000) return 'Just now'
  if (diff < 60000) return `${Math.floor(diff / 1000)}s ago`
  return `${Math.floor(diff / 60000)}m ago`
}

// Auto-expand on error
watch(() => status.status.value, (newStatus) => {
  if (newStatus === 'error' && props.autoExpand) {
    expanded.value = true
  }
})
</script>

<style scoped>
.socket-status {
  position: relative;
  z-index: 50;
}

/* Smooth transitions */
.socket-status * {
  transition: all 0.2s ease-in-out;
}

/* Pulse animation for connecting state */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
